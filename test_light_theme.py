#!/usr/bin/env python3
"""
测试系统light theme状态检查功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


def test_light_theme_status():
    """测试light theme状态检查"""
    print("=" * 60)
    print("测试系统Light Theme状态检查功能")
    print("=" * 60)
    
    # 创建状态检查器实例
    checker = SystemStatusChecker()
    
    try:
        # 测试基本状态检查
        print("\n1. 检查基本Light Theme状态:")
        print("-" * 40)
        is_light = checker.check_light_theme_status()
        print(f"系统是否为浅色主题: {is_light}")
        print(f"主题模式: {'浅色主题' if is_light else '深色主题'}")
        
        # 测试详细状态获取
        print("\n2. 获取详细主题状态信息:")
        print("-" * 40)
        detailed_status = checker.get_light_theme_detailed_status()
        
        print(f"是否为浅色主题: {detailed_status['is_light_theme']}")
        print(f"UI夜间模式值: {detailed_status['ui_night_mode']}")
        print(f"UI夜间模式描述: {detailed_status['ui_night_mode_description']}")
        print(f"主题模式: {detailed_status['theme_mode']}")
        print(f"自动模式启用: {detailed_status['auto_mode_enabled']}")
        if detailed_status['auto_mode_current_status']:
            print(f"自动模式当前状态: {detailed_status['auto_mode_current_status']}")
        print(f"检测方法: {detailed_status['detection_method']}")
        
        # 显示系统属性
        if detailed_status['system_properties']:
            print(f"\n系统属性:")
            for prop, value in detailed_status['system_properties'].items():
                print(f"  {prop}: {value}")
        
        # 显示配置信息
        if detailed_status['configuration_info']:
            print(f"\n配置信息:")
            for key, value in detailed_status['configuration_info'].items():
                print(f"  {key}: {value}")
        
        print("\n" + "=" * 60)
        print("测试完成!")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        log.error(f"Light theme状态测试失败: {e}")
        return False


def test_auto_theme_detection():
    """测试自动模式主题检测"""
    print("\n" + "=" * 60)
    print("测试自动模式主题检测功能")
    print("=" * 60)
    
    checker = SystemStatusChecker()
    
    try:
        # 直接测试自动模式检测方法
        print("\n测试自动模式当前状态检测:")
        print("-" * 40)
        auto_status = checker._check_auto_theme_current_status()
        print(f"自动模式当前状态: {'浅色主题' if auto_status else '深色主题'}")
        
        return True
        
    except Exception as e:
        print(f"自动模式检测测试失败: {e}")
        log.error(f"自动模式检测测试失败: {e}")
        return False


if __name__ == "__main__":
    print("开始测试Light Theme状态检查功能...")
    
    # 测试基本功能
    success1 = test_light_theme_status()
    
    # 测试自动模式检测
    success2 = test_auto_theme_detection()
    
    if success1 and success2:
        print("\n✅ 所有测试通过!")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败!")
        sys.exit(1)

#!/usr/bin/env python3
"""
Ella应用检测问题调试脚本
分析Ella应用未启动时返回True的原因，特别是后台保活进程的影响
"""

import subprocess
import time
import sys
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from core.logger import log
from pages.base.app_detector import AppDetector, AppType
from tools.adb_process_monitor import AdbProcessMonitor


def check_ella_processes():
    """检查Ella相关的所有进程"""
    print("\n🔍 检查Ella相关进程")
    print("=" * 50)
    
    ella_package = "com.transsion.aivoiceassistant"
    
    try:
        # 1. 使用ps命令检查进程
        result = subprocess.run(
            ["adb", "shell", "ps", "|", "grep", "aivoice"],
            capture_output=True,
            text=True,
            timeout=10,
            shell=True
        )
        
        if result.returncode == 0 and result.stdout.strip():
            print("📱 找到aivoice相关进程:")
            lines = result.stdout.strip().split('\n')
            for line in lines:
                print(f"  {line}")
        else:
            print("❌ 未找到aivoice相关进程")
            
        # 2. 检查完整的Ella包名进程
        result2 = subprocess.run(
            ["adb", "shell", "ps", "|", "grep", ella_package],
            capture_output=True,
            text=True,
            timeout=10,
            shell=True
        )
        
        if result2.returncode == 0 and result2.stdout.strip():
            print(f"\n📱 找到{ella_package}进程:")
            lines = result2.stdout.strip().split('\n')
            for line in lines:
                print(f"  {line}")
        else:
            print(f"❌ 未找到{ella_package}进程")
            
    except Exception as e:
        print(f"检查进程失败: {e}")


def check_ella_services():
    """检查Ella相关的系统服务"""
    print("\n🔧 检查Ella相关服务")
    print("=" * 50)
    
    try:
        # 检查系统服务
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "services", "|", "grep", "-i", "ella"],
            capture_output=True,
            text=True,
            timeout=10,
            shell=True
        )
        
        if result.returncode == 0 and result.stdout.strip():
            print("🔧 找到Ella相关服务:")
            lines = result.stdout.strip().split('\n')
            for line in lines[:10]:  # 只显示前10行
                print(f"  {line}")
        else:
            print("❌ 未找到Ella相关服务")
            
        # 检查aivoice相关服务
        result2 = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "services", "|", "grep", "-i", "aivoice"],
            capture_output=True,
            text=True,
            timeout=10,
            shell=True
        )
        
        if result2.returncode == 0 and result2.stdout.strip():
            print("\n🔧 找到aivoice相关服务:")
            lines = result2.stdout.strip().split('\n')
            for line in lines[:10]:  # 只显示前10行
                print(f"  {line}")
        else:
            print("❌ 未找到aivoice相关服务")
            
    except Exception as e:
        print(f"检查服务失败: {e}")


def check_ella_activities():
    """检查Ella相关的Activity状态"""
    print("\n📱 检查Ella Activity状态")
    print("=" * 50)
    
    ella_package = "com.transsion.aivoiceassistant"
    
    try:
        # 检查当前Activity
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "activities"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            output = result.stdout
            
            # 查找Ella相关的Activity
            lines = output.split('\n')
            ella_activities = []
            
            for i, line in enumerate(lines):
                if ella_package in line or "ella" in line.lower():
                    # 收集相关的上下文行
                    start = max(0, i-2)
                    end = min(len(lines), i+3)
                    context = lines[start:end]
                    ella_activities.extend(context)
            
            if ella_activities:
                print("📱 找到Ella相关Activity:")
                for line in ella_activities[:20]:  # 限制输出行数
                    print(f"  {line}")
            else:
                print("❌ 未找到Ella相关Activity")
                
        else:
            print(f"获取Activity信息失败: {result.stderr}")
            
    except Exception as e:
        print(f"检查Activity失败: {e}")


def check_ella_recent_tasks():
    """检查Ella在最近任务中的状态"""
    print("\n📋 检查Ella最近任务状态")
    print("=" * 50)
    
    ella_package = "com.transsion.aivoiceassistant"
    
    try:
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "recents"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            output = result.stdout
            
            if ella_package in output:
                print("📋 在最近任务中找到Ella:")
                lines = output.split('\n')
                for line in lines:
                    if ella_package in line:
                        print(f"  {line}")
            else:
                print("❌ 最近任务中未找到Ella")
                
        else:
            print(f"获取最近任务失败: {result.stderr}")
            
    except Exception as e:
        print(f"检查最近任务失败: {e}")


def test_detection_methods():
    """测试不同的检测方法"""
    print("\n🧪 测试不同检测方法")
    print("=" * 50)
    
    ella_package = "com.transsion.aivoiceassistant"
    
    # 1. 使用AppDetector
    print("1️⃣ 使用AppDetector检测:")
    try:
        app_detector = AppDetector()
        result1 = app_detector.check_app_opened(AppType.ELLA)
        print(f"  AppDetector结果: {result1}")
    except Exception as e:
        print(f"  AppDetector异常: {e}")
    
    # 2. 使用AdbProcessMonitor的传统方法
    print("\n2️⃣ 使用AdbProcessMonitor传统方法:")
    try:
        monitor = AdbProcessMonitor()
        result2 = monitor.is_package_running(ella_package, use_fast_method=True)
        print(f"  传统检测结果: {result2}")
    except Exception as e:
        print(f"  传统检测异常: {e}")
    
    # 3. 使用AdbProcessMonitor的活跃检测
    print("\n3️⃣ 使用AdbProcessMonitor活跃检测:")
    try:
        result3 = monitor.is_package_actively_running(ella_package)
        print(f"  活跃检测结果: {result3}")
    except Exception as e:
        print(f"  活跃检测异常: {e}")
    
    # 4. 手动检查当前前台应用
    print("\n4️⃣ 检查当前前台应用:")
    try:
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "window", "windows", "|", "grep", "mCurrentFocus"],
            capture_output=True,
            text=True,
            timeout=5,
            shell=True
        )
        
        if result.returncode == 0 and result.stdout.strip():
            focus_line = result.stdout.strip()
            print(f"  当前焦点: {focus_line}")
            
            if ella_package in focus_line:
                print("  ✅ Ella在前台")
            else:
                print("  ❌ Ella不在前台")
        else:
            print("  无法获取焦点信息")
            
    except Exception as e:
        print(f"  检查前台应用异常: {e}")


def analyze_detection_issue():
    """分析检测问题的根本原因"""
    print("\n🔍 分析检测问题")
    print("=" * 50)
    
    ella_package = "com.transsion.aivoiceassistant"
    
    try:
        monitor = AdbProcessMonitor()
        
        # 获取详细的包状态信息
        status_info = monitor.check_package_status(ella_package)
        
        print("📊 详细状态信息:")
        for key, value in status_info.items():
            print(f"  {key}: {value}")
        
        # 分析可能的误判原因
        print("\n🔍 可能的误判原因分析:")
        
        if status_info.get('is_running', False):
            print("  ⚠️ 检测到应用正在运行")
            
            if status_info.get('pids'):
                print(f"  📍 进程PID: {status_info['pids']}")
                
                # 检查每个PID的详细信息
                for pid in status_info['pids']:
                    try:
                        # 检查进程状态
                        result = subprocess.run(
                            ["adb", "shell", "cat", f"/proc/{pid}/stat"],
                            capture_output=True,
                            text=True,
                            timeout=3
                        )
                        
                        if result.returncode == 0:
                            stat_parts = result.stdout.split()
                            if len(stat_parts) > 2:
                                state = stat_parts[2]
                                print(f"    PID {pid} 状态: {state}")
                                
                                if state == 'S':
                                    print(f"    -> 睡眠状态（可能是后台服务）")
                                elif state == 'R':
                                    print(f"    -> 运行状态")
                                elif state == 'Z':
                                    print(f"    -> 僵尸进程")
                                    
                        # 检查进程命令行
                        result2 = subprocess.run(
                            ["adb", "shell", "cat", f"/proc/{pid}/cmdline"],
                            capture_output=True,
                            text=True,
                            timeout=3
                        )
                        
                        if result2.returncode == 0:
                            cmdline = result2.stdout.replace('\x00', ' ').strip()
                            print(f"    PID {pid} 命令行: {cmdline}")
                            
                    except Exception as e:
                        print(f"    检查PID {pid} 失败: {e}")
        else:
            print("  ✅ 未检测到应用运行")
            
    except Exception as e:
        print(f"分析失败: {e}")


def main():
    """主函数"""
    print("🔍 Ella应用检测问题调试")
    print("=" * 60)
    
    # 检查ADB连接
    try:
        result = subprocess.run(["adb", "devices"], capture_output=True, text=True, timeout=5)
        if "device" not in result.stdout:
            print("❌ ADB设备未连接")
            return
        print("✅ ADB设备已连接")
    except Exception as e:
        print(f"❌ ADB连接检查失败: {e}")
        return
    
    # 执行各种检查
    check_ella_processes()
    check_ella_services()
    check_ella_activities()
    check_ella_recent_tasks()
    test_detection_methods()
    analyze_detection_issue()
    
    print("\n" + "=" * 60)
    print("🎯 调试完成")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试移动数据状态检查功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


def test_mobile_data_status():
    """测试移动数据状态检查功能"""
    
    print("=" * 60)
    print("测试移动数据状态检查功能")
    print("=" * 60)
    
    # 创建状态检查器实例（不需要driver）
    checker = SystemStatusChecker()
    
    try:
        # 测试基本移动数据状态
        print("\n1. 测试基本移动数据状态:")
        print("-" * 40)
        mobile_data_enabled = checker.check_mobile_data_status()
        print(f"移动数据状态: {'开启' if mobile_data_enabled else '关闭'}")
        
        # 测试移动数据详细状态
        print("\n2. 测试移动数据详细状态:")
        print("-" * 40)
        mobile_data_info = checker.check_mobile_data_connection_status()
        
        print("移动数据详细信息:")
        for key, value in mobile_data_info.items():
            print(f"  {key}: {value}")
            
        # 总结
        print("\n" + "=" * 60)
        print("测试总结:")
        print(f"✅ 移动数据基本状态检查: {'成功' if mobile_data_enabled is not None else '失败'}")
        print(f"✅ 移动数据详细状态检查: {'成功' if mobile_data_info else '失败'}")
        
        if mobile_data_info.get('mobile_data_enabled'):
            print("📱 移动数据当前已开启")
            if mobile_data_info.get('connected'):
                print("🌐 移动数据已连接")
                if mobile_data_info.get('network_type'):
                    print(f"📶 网络类型: {mobile_data_info['network_type']}")
                if mobile_data_info.get('operator_name'):
                    print(f"📡 运营商: {mobile_data_info['operator_name']}")
                if mobile_data_info.get('signal_strength'):
                    print(f"📊 信号强度: {mobile_data_info['signal_strength']} dBm")
            else:
                print("❌ 移动数据未连接")
        else:
            print("📱 移动数据当前已关闭")
            
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        log.error(f"测试移动数据状态失败: {e}")


if __name__ == "__main__":
    test_mobile_data_status()

#!/usr/bin/env python3
"""
测试移动数据状态检查修复
"""

from pages.base.system_status_checker import SystemStatusChecker

def test_mobile_data_fix():
    print('=' * 60)
    print('移动数据状态检查 - 修复验证')
    print('=' * 60)

    checker = SystemStatusChecker()

    # 基本检查
    print('\n1. 基本移动数据状态检查:')
    result = checker.check_mobile_data_status()
    print(f'结果: {result}')
    print(f'预期: False (因为SIM卡未插入)')
    if result == False:
        print('验证: ✅ 正确')
    else:
        print('验证: ❌ 错误')

    # 详细检查
    print('\n2. 详细状态信息:')
    detail = checker.check_mobile_data_connection_status()
    print(f'移动数据启用: {detail["mobile_data_enabled"]}')
    print(f'SIM卡状态: {detail["sim_state"]}')
    print(f'SIM卡可用: {detail["sim_available"]}')

    print('\n' + '=' * 60)
    print('修复验证完成')
    print('=' * 60)

if __name__ == "__main__":
    test_mobile_data_fix()

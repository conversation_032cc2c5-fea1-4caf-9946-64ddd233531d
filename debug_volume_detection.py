#!/usr/bin/env python3
"""
调试音量检测问题
"""

import subprocess
import sys
import os
import re

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_volume_settings():
    """检查音量设置"""
    print("=" * 60)
    print("检查音量设置")
    print("=" * 60)
    
    volume_settings = [
        "volume_music",
        "volume_ring", 
        "volume_alarm",
        "volume_notification",
        "volume_system",
        "volume_voice"
    ]
    
    for setting in volume_settings:
        try:
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "system", setting],
                capture_output=True,
                text=True,
                timeout=3
            )
            
            if result.returncode == 0:
                value = result.stdout.strip()
                print(f"{setting}: {value}")
        except Exception as e:
            print(f"{setting}: 获取失败 - {e}")

def check_audio_dumpsys():
    """检查audio dumpsys输出"""
    print("\n" + "=" * 60)
    print("检查 dumpsys audio 输出")
    print("=" * 60)
    
    try:
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "audio"],
            capture_output=True,
            text=True,
            timeout=15
        )
        
        if result.returncode == 0:
            audio_output = result.stdout
            
            # 查找音量相关信息
            lines = audio_output.split('\n')
            volume_lines = []
            
            for line in lines:
                if any(keyword in line.lower() for keyword in ['volume', 'stream', 'music', 'level']):
                    volume_lines.append(line.strip())
            
            print("音量相关信息:")
            for line in volume_lines[:20]:  # 显示前20行
                if line:
                    print(f"  {line}")
                    
            # 特别查找STREAM_MUSIC信息
            print("\n特别查找 STREAM_MUSIC 信息:")
            music_matches = re.findall(r'STREAM_MUSIC.*', audio_output, re.IGNORECASE)
            for match in music_matches[:5]:
                print(f"  {match}")
                
        else:
            print(f"dumpsys audio 失败: {result.stderr}")
    except Exception as e:
        print(f"检查 dumpsys audio 失败: {e}")

def check_media_session():
    """检查media session"""
    print("\n" + "=" * 60)
    print("检查 dumpsys media_session 输出")
    print("=" * 60)
    
    try:
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "media_session"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            media_output = result.stdout
            
            # 查找音量相关信息
            volume_matches = re.findall(r'.*volume.*', media_output, re.IGNORECASE)
            if volume_matches:
                print("媒体会话音量信息:")
                for match in volume_matches[:10]:
                    print(f"  {match.strip()}")
            else:
                print("未找到媒体会话音量信息")
        else:
            print(f"dumpsys media_session 失败: {result.stderr}")
    except Exception as e:
        print(f"检查 dumpsys media_session 失败: {e}")

def check_current_volume_detection():
    """检查当前音量检测方法"""
    print("\n" + "=" * 60)
    print("当前音量检测方法结果")
    print("=" * 60)
    
    from pages.base.system_status_checker import SystemStatusChecker
    
    checker = SystemStatusChecker()
    
    print("基本音量检测:")
    volume = checker.get_system_volume()
    print(f"  媒体音量: {volume}")
    
    print("\n详细音量状态:")
    detailed = checker.get_system_volume_detailed_status()
    print(f"  检测方法: {detailed.get('detection_method', 'Unknown')}")
    print(f"  媒体音量: {detailed.get('media_volume', -1)}")
    print(f"  铃声音量: {detailed.get('ring_volume', -1)}")
    print(f"  通知音量: {detailed.get('notification_volume', -1)}")

def test_alternative_volume_methods():
    """测试其他音量获取方法"""
    print("\n" + "=" * 60)
    print("测试其他音量获取方法")
    print("=" * 60)
    
    # 方法1: 通过service call获取音量
    print("1. 通过 service call 获取音量:")
    try:
        result = subprocess.run(
            ["adb", "shell", "service", "call", "audio", "25", "i32", "3"],  # 3 = STREAM_MUSIC
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            output = result.stdout.strip()
            print(f"   service call 结果: {output}")
            
            # 解析结果
            if "Result: Parcel" in output:
                # 提取数字
                numbers = re.findall(r'0x[0-9a-f]+|(\d+)', output)
                if numbers:
                    print(f"   提取的数字: {numbers}")
        else:
            print(f"   service call 失败: {result.stderr}")
    except Exception as e:
        print(f"   service call 错误: {e}")
    
    # 方法2: 通过am命令获取音量
    print("\n2. 通过 am 命令获取音量:")
    try:
        result = subprocess.run(
            ["adb", "shell", "am", "broadcast", "-a", "android.media.VOLUME_CHANGED_ACTION"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            print(f"   am broadcast 成功: {result.stdout.strip()}")
        else:
            print(f"   am broadcast 失败: {result.stderr}")
    except Exception as e:
        print(f"   am broadcast 错误: {e}")

if __name__ == "__main__":
    print("开始调试音量检测问题...")
    
    # 1. 检查音量设置
    check_volume_settings()
    
    # 2. 检查audio dumpsys
    check_audio_dumpsys()
    
    # 3. 检查media session
    check_media_session()
    
    # 4. 检查当前检测方法
    check_current_volume_detection()
    
    # 5. 测试其他方法
    test_alternative_volume_methods()
    
    print("\n" + "=" * 60)
    print("调试完成")
    print("=" * 60)

# 系统Light Theme状态检查功能

## 功能概述

参考现有的蓝牙状态检查方法，实现了获取Android系统light theme（浅色主题）状态的功能。该功能可以检测系统当前是否处于浅色主题模式，支持固定模式和自动模式的检测。

## 实现的方法

### 1. `check_light_theme_status()` - 基本主题状态检查

```python
def check_light_theme_status(self) -> bool:
    """
    检查系统light theme状态
    
    Returns:
        bool: 系统是否为浅色主题模式
    """
```

**功能特点：**
- 通过ADB命令 `settings get secure ui_night_mode` 检查UI夜间模式状态
- 支持三种模式：
  - `0` = 浅色主题（关闭夜间模式）
  - `1` = 深色主题（开启夜间模式）
  - `2` = 自动模式（跟随系统设置）
- 自动模式下会进一步检查当前实际状态
- 异常情况下默认返回浅色主题

### 2. `_check_auto_theme_current_status()` - 自动模式状态检测

```python
def _check_auto_theme_current_status(self) -> bool:
    """
    检查自动模式下的当前主题状态
    
    Returns:
        bool: 当前是否为浅色主题
    """
```

**检测方法：**
- **方法1**: 通过 `dumpsys uimode` 检查UiModeManager状态
  - 解析 `mNightMode` 参数
  - 解析Configuration中的uiMode位掩码
- **方法2**: 通过系统属性 `ro.config.ui_night_mode` 检查
- 多重检测确保准确性

### 3. `get_light_theme_detailed_status()` - 详细状态信息

```python
def get_light_theme_detailed_status(self) -> dict:
    """
    获取系统主题详细状态信息
    
    Returns:
        dict: 系统主题详细状态信息
    """
```

**返回信息包括：**
- `is_light_theme`: 是否为浅色主题
- `ui_night_mode`: UI夜间模式值
- `ui_night_mode_description`: 模式描述
- `theme_mode`: 主题模式（LIGHT/DARK/AUTO）
- `auto_mode_enabled`: 是否启用自动模式
- `auto_mode_current_status`: 自动模式当前状态
- `detection_method`: 检测方法
- `system_properties`: 相关系统属性
- `configuration_info`: 配置信息

## 使用示例

### 基本使用

```python
from pages.base.system_status_checker import SystemStatusChecker

checker = SystemStatusChecker()

# 检查是否为浅色主题
is_light_theme = checker.check_light_theme_status()
if is_light_theme:
    print("当前为浅色主题")
else:
    print("当前为深色主题")
```

### 获取详细信息

```python
# 获取详细主题信息
theme_info = checker.get_light_theme_detailed_status()
print(f"主题模式: {theme_info['theme_mode']}")
print(f"自动模式: {theme_info['auto_mode_enabled']}")
print(f"检测方法: {theme_info['detection_method']}")
```

### 在测试用例中使用

```python
def test_ui_elements():
    checker = SystemStatusChecker()
    is_light = checker.check_light_theme_status()
    
    if is_light:
        # 验证浅色主题下的UI元素
        assert element.background_color == "#FFFFFF"
        assert element.text_color == "#000000"
    else:
        # 验证深色主题下的UI元素
        assert element.background_color == "#000000"
        assert element.text_color == "#FFFFFF"
```

## 技术实现细节

### ADB命令支持
- `adb shell settings get secure ui_night_mode` - 获取UI夜间模式设置
- `adb shell dumpsys uimode` - 获取UiModeManager详细信息
- `adb shell getprop ro.config.ui_night_mode` - 获取系统属性

### 错误处理
- 网络超时处理（5-10秒超时）
- ADB命令执行失败的降级处理
- 异常情况下的默认值返回
- 详细的日志记录

### 兼容性
- 支持Android 10+的深色主题功能
- 兼容不同厂商的Android系统
- 支持自动模式和固定模式

## 测试验证

项目包含完整的测试文件：
- `test_light_theme.py` - 功能测试脚本
- `example_light_theme_usage.py` - 使用示例和演示

运行测试：
```bash
python test_light_theme.py
python example_light_theme_usage.py
```

## 集成说明

该功能已集成到 `pages/base/system_status_checker.py` 中，与现有的蓝牙、WiFi、位置服务等状态检查功能保持一致的API设计和代码风格。

可以与其他系统状态检查功能配合使用，为自动化测试提供完整的系统环境感知能力。

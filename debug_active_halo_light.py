#!/usr/bin/env python3
"""
调试 Active Halo Light 的真实设置名称
"""

import subprocess
import sys
import os

def search_all_light_settings():
    """搜索所有可能的 light 相关设置"""
    print("=" * 60)
    print("搜索所有可能的 light 相关设置")
    print("=" * 60)
    
    keywords = ['light', 'halo', 'led', 'notification', 'active']
    all_found_settings = []
    
    for namespace in ["system", "secure", "global"]:
        print(f"\n{namespace} 命名空间:")
        print("-" * 30)
        
        try:
            result = subprocess.run(
                ["adb", "shell", "settings", "list", namespace],
                capture_output=True,
                text=True,
                timeout=15
            )
            
            if result.returncode == 0:
                settings = result.stdout.split('\n')
                
                # 查找包含关键词的设置
                relevant_settings = []
                for setting in settings:
                    setting = setting.strip()
                    if setting and any(keyword in setting.lower() for keyword in keywords):
                        relevant_settings.append(setting)
                
                if relevant_settings:
                    for setting in relevant_settings[:20]:  # 显示前20个
                        print(f"  {setting}")
                        all_found_settings.append((namespace, setting))
                else:
                    print("  未找到相关设置")
        except Exception as e:
            print(f"  搜索失败: {e}")
    
    return all_found_settings

def check_specific_settings():
    """检查特定的可能设置名称"""
    print("\n" + "=" * 60)
    print("检查特定的可能设置名称")
    print("=" * 60)
    
    possible_settings = [
        # 可能的 active halo light 变体
        ("system", "active_halo_light"),
        ("system", "activehalolight"),
        ("system", "active_halo_lighting"),
        ("system", "halo_light_active"),
        ("system", "halo_lighting_active"),
        ("system", "notification_halo_light"),
        ("system", "halo_notification_light"),
        
        # secure 命名空间
        ("secure", "active_halo_light"),
        ("secure", "halo_light_enabled"),
        ("secure", "notification_halo_enabled"),
        
        # global 命名空间
        ("global", "active_halo_light"),
        ("global", "halo_light_setting"),
        
        # 其他可能的名称
        ("system", "notification_light_pulse"),
        ("system", "led_notification_enabled"),
        ("system", "breathing_light_enabled")
    ]
    
    found_values = []
    
    for namespace, setting in possible_settings:
        try:
            result = subprocess.run(
                ["adb", "shell", "settings", "get", namespace, setting],
                capture_output=True,
                text=True,
                timeout=3
            )
            
            if result.returncode == 0:
                value = result.stdout.strip()
                print(f"{namespace}.{setting}: {value}")
                if value and value != "null":
                    found_values.append((namespace, setting, value))
        except:
            print(f"{namespace}.{setting}: (检查失败)")
    
    return found_values

def check_system_properties():
    """检查系统属性"""
    print("\n" + "=" * 60)
    print("检查相关系统属性")
    print("=" * 60)
    
    properties = [
        "persist.sys.active_halo_light",
        "persist.vendor.halo.light",
        "ro.vendor.halo_light.support",
        "persist.sys.notification.halo",
        "vendor.notification.halo.enable"
    ]
    
    found_props = []
    
    for prop in properties:
        try:
            result = subprocess.run(
                ["adb", "shell", "getprop", prop],
                capture_output=True,
                text=True,
                timeout=3
            )
            
            if result.returncode == 0:
                value = result.stdout.strip()
                print(f"{prop}: {value}")
                if value and value not in ["", "null"]:
                    found_props.append((prop, value))
        except:
            print(f"{prop}: (检查失败)")
    
    return found_props

def check_current_detection():
    """检查当前的检测结果"""
    print("\n" + "=" * 60)
    print("当前检测方法结果")
    print("=" * 60)
    
    sys.path.insert(0, '.')
    from pages.base.system_status_checker import SystemStatusChecker
    
    checker = SystemStatusChecker()
    
    print("基本检测:")
    result = checker.check_active_halo_lighting_status()
    print(f"  检测结果: {result}")
    print(f"  预期结果: True")
    print(f"  是否正确: {'✅' if result == True else '❌'}")
    
    print("\n详细状态:")
    detailed = checker.get_active_halo_lighting_detailed_status()
    print(f"  检测方法: {detailed['detection_method']}")
    print(f"  系统设置数量: {len(detailed['system_settings'])}")
    print(f"  安全设置数量: {len(detailed['secure_settings'])}")

if __name__ == "__main__":
    print("开始调试 Active Halo Light 真实设置...")
    
    # 1. 搜索所有相关设置
    all_settings = search_all_light_settings()
    
    # 2. 检查特定设置
    found_values = check_specific_settings()
    
    # 3. 检查系统属性
    found_props = check_system_properties()
    
    # 4. 检查当前检测结果
    check_current_detection()
    
    print("\n" + "=" * 60)
    print("调试总结")
    print("=" * 60)
    print(f"找到相关设置: {len(all_settings)} 个")
    print(f"找到有值的设置: {len(found_values)} 个")
    print(f"找到有值的属性: {len(found_props)} 个")
    
    if found_values:
        print("\n有值的设置:")
        for namespace, setting, value in found_values:
            status = "开启" if value == "1" else "关闭" if value == "0" else f"值:{value}"
            print(f"  {namespace}.{setting} = {value} ({status})")
    
    if found_props:
        print("\n有值的属性:")
        for prop, value in found_props:
            print(f"  {prop} = {value}")
    
    print("\n建议: 根据上述结果确定正确的设置名称")

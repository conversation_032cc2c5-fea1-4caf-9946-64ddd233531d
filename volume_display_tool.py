#!/usr/bin/env python3
"""
音量显示工具 - 正确显示音量百分比
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


class VolumeDisplayTool:
    """音量显示工具类"""
    
    def __init__(self):
        self.checker = SystemStatusChecker()
    
    def get_volume_display_info(self, volume_type: str = "music") -> dict:
        """
        获取用于显示的音量信息
        
        Args:
            volume_type (str): 音量类型
            
        Returns:
            dict: 显示信息
        """
        try:
            volume_info = self.checker.get_volume_with_percentage(volume_type)
            
            current = volume_info['current_volume']
            max_vol = volume_info['max_volume']
            percentage = volume_info['percentage']
            is_max = volume_info['is_max']
            
            # 生成显示信息
            display_info = {
                'raw_value': current,
                'max_value': max_vol,
                'percentage': percentage,
                'is_max_volume': is_max,
                'display_text': self._generate_display_text(current, max_vol, percentage, is_max),
                'progress_bar': self._generate_progress_bar(percentage),
                'status_icon': self._get_status_icon(percentage, is_max),
                'status_description': self._get_status_description(percentage, is_max),
                'recommendation': self._get_recommendation(current, max_vol, percentage, is_max)
            }
            
            return display_info
            
        except Exception as e:
            log.error(f"获取{volume_type}显示信息失败: {e}")
            return {
                'raw_value': -1,
                'max_value': -1,
                'percentage': 0,
                'is_max_volume': False,
                'display_text': '获取失败',
                'progress_bar': '❌',
                'status_icon': '❓',
                'status_description': '无法获取',
                'recommendation': '请检查设备连接'
            }
    
    def _generate_display_text(self, current: int, max_vol: int, percentage: float, is_max: bool) -> str:
        """生成显示文本"""
        if current < 0:
            return "获取失败"
        
        if max_vol > 0:
            text = f"{percentage:.0f}%"
            if is_max:
                text += " (最大)"
            return text
        else:
            return f"{current} (未知范围)"
    
    def _generate_progress_bar(self, percentage: float) -> str:
        """生成进度条"""
        if percentage < 0:
            return "❌"
        
        filled = int(percentage / 10)
        empty = 10 - filled
        return "█" * filled + "░" * empty + f" {percentage:.0f}%"
    
    def _get_status_icon(self, percentage: float, is_max: bool) -> str:
        """获取状态图标"""
        if percentage < 0:
            return "❓"
        elif is_max:
            return "📢"
        elif percentage >= 80:
            return "🔊"
        elif percentage >= 50:
            return "🔉"
        elif percentage >= 20:
            return "🔈"
        elif percentage > 0:
            return "🔇"
        else:
            return "🔕"
    
    def _get_status_description(self, percentage: float, is_max: bool) -> str:
        """获取状态描述"""
        if percentage < 0:
            return "无法获取"
        elif is_max:
            return "最大音量"
        elif percentage >= 80:
            return "很高"
        elif percentage >= 50:
            return "中等"
        elif percentage >= 20:
            return "较低"
        elif percentage > 0:
            return "很低"
        else:
            return "静音"
    
    def _get_recommendation(self, current: int, max_vol: int, percentage: float, is_max: bool) -> str:
        """获取建议"""
        if current < 0:
            return "请检查设备连接和权限"
        elif is_max:
            return f"音量已达最大值 ({current})"
        elif percentage < 30:
            return f"音量较低，可调高到{max_vol}以获得最佳效果"
        elif percentage < 80:
            return f"音量适中，最大可调至{max_vol}"
        else:
            return f"音量较高，接近最大值{max_vol}"
    
    def display_all_volumes(self):
        """显示所有音量类型的信息"""
        print("=" * 80)
        print("系统音量状态显示")
        print("=" * 80)
        
        volume_types = {
            "music": "媒体音量",
            "ring": "铃声音量",
            "alarm": "闹钟音量",
            "notification": "通知音量",
            "system": "系统音量",
            "voice_call": "通话音量"
        }
        
        for vol_type, vol_name in volume_types.items():
            print(f"\n📱 {vol_name}:")
            print("-" * 40)
            
            display_info = self.get_volume_display_info(vol_type)
            
            print(f"   {display_info['status_icon']} 当前音量: {display_info['display_text']}")
            print(f"   📊 进度条: {display_info['progress_bar']}")
            print(f"   📝 状态: {display_info['status_description']}")
            print(f"   💡 建议: {display_info['recommendation']}")
            
            # 显示原始值（用于调试）
            if display_info['raw_value'] >= 0:
                print(f"   🔧 原始值: {display_info['raw_value']}/{display_info['max_value']}")
    
    def display_media_volume_focus(self):
        """重点显示媒体音量信息"""
        print("\n" + "=" * 80)
        print("媒体音量详细信息")
        print("=" * 80)
        
        display_info = self.get_volume_display_info("music")
        
        print(f"\n🎵 当前媒体音量状态:")
        print(f"   显示值: {display_info['display_text']}")
        print(f"   原始值: {display_info['raw_value']}")
        print(f"   最大值: {display_info['max_value']}")
        print(f"   百分比: {display_info['percentage']:.1f}%")
        print(f"   是否最大: {'是' if display_info['is_max_volume'] else '否'}")
        
        print(f"\n📊 可视化显示:")
        print(f"   进度条: {display_info['progress_bar']}")
        print(f"   状态图标: {display_info['status_icon']} ({display_info['status_description']})")
        
        print(f"\n💡 使用建议:")
        print(f"   {display_info['recommendation']}")
        
        # 解释为什么显示13而不是100%
        if display_info['raw_value'] == 13 and not display_info['is_max_volume']:
            print(f"\n❓ 为什么显示13而不是100%?")
            print(f"   • 系统音量范围是 0 到 {display_info['max_value']}")
            print(f"   • 当前设置为 {display_info['raw_value']}，相当于 {display_info['percentage']:.1f}%")
            print(f"   • 要达到100%，需要调整到 {display_info['max_value']}")
            print(f"   • 建议显示百分比 ({display_info['percentage']:.0f}%) 而不是原始值 ({display_info['raw_value']})")


def main():
    """主函数"""
    
    tool = VolumeDisplayTool()
    
    # 显示所有音量
    tool.display_all_volumes()
    
    # 重点显示媒体音量
    tool.display_media_volume_focus()
    
    print("\n" + "=" * 80)
    print("总结")
    print("=" * 80)
    
    print("\n🎯 关键要点:")
    print("   1. 音量值13可能不是最大值，需要获取音量范围")
    print("   2. 建议显示百分比而不是原始数值")
    print("   3. 不同音量类型有不同的最大值范围")
    print("   4. 使用 get_volume_with_percentage() 方法获取准确信息")
    
    print("\n🔧 推荐的显示方式:")
    print("   • 用户界面: 显示百分比 (如: 87%)")
    print("   • 调试信息: 显示原始值 (如: 13/15)")
    print("   • 进度条: 可视化显示音量级别")
    print("   • 状态图标: 直观表示音量高低")


if __name__ == "__main__":
    main()

# 系统位置服务状态获取功能

## 概述

参考 `check_bluetooth_status` 方法的实现方式，新增了系统位置服务状态获取功能，包括两个主要方法：

1. `check_location_status()` - 检查位置服务是否开启
2. `get_location_detailed_status()` - 获取位置服务的详细状态信息

## 功能特性

### 支持的检测内容

- **位置服务开关状态**: 检查位置服务是否启用
- **位置模式**: 关闭/仅设备/省电模式/高精度模式
- **位置提供者**: GPS、网络定位、被动定位等
- **模拟位置**: 检测是否启用了开发者模拟位置
- **位置精度**: 获取位置精度信息
- **最后已知位置**: 获取最后记录的位置信息

### 多种检测方法

类似于蓝牙状态检测，位置服务获取使用多种方法确保兼容性：

1. **ADB Settings命令**: 通过 `settings get secure location_mode` 获取
2. **位置提供者**: 通过 `settings get secure location_providers_allowed` 获取
3. **LocationManager Dumpsys**: 通过 `dumpsys location` 解析详细信息
4. **系统属性**: 通过 `getprop` 获取位置相关属性

## 位置模式说明

Android系统的位置模式有以下几种：

- **0 - 关闭**: 位置服务完全关闭
- **1 - 仅设备**: 仅使用GPS，不使用网络定位
- **2 - 省电模式**: 使用网络定位，不使用GPS
- **3 - 高精度模式**: 同时使用GPS和网络定位

## 使用方法

### 基本用法

```python
from pages.base.system_status_checker import SystemStatusChecker

# 创建检查器实例
checker = SystemStatusChecker()

# 检查位置服务是否开启
location_enabled = checker.check_location_status()
print(f"位置服务: {'开启' if location_enabled else '关闭'}")
```

### 详细状态获取

```python
# 获取位置服务详细状态
detailed_status = checker.get_location_detailed_status()

print(f"位置服务启用: {detailed_status['location_enabled']}")
print(f"位置模式: {detailed_status['location_mode_description']}")
print(f"GPS启用: {detailed_status['gps_enabled']}")
print(f"网络定位启用: {detailed_status['network_location_enabled']}")
print(f"允许的提供者: {detailed_status['providers_allowed']}")
print(f"模拟位置启用: {detailed_status['mock_location_enabled']}")
```

### 返回值说明

- **check_location_status()**: 返回布尔值，True表示位置服务已开启
- **get_location_detailed_status()**: 返回包含以下信息的字典：
  - `location_enabled`: 位置服务是否启用
  - `location_mode`: 位置模式值 (0-3)
  - `location_mode_description`: 位置模式描述
  - `providers_allowed`: 允许的位置提供者列表
  - `active_providers`: 活跃的位置提供者列表
  - `gps_enabled`: GPS是否启用
  - `network_location_enabled`: 网络定位是否启用
  - `passive_location_enabled`: 被动定位是否启用
  - `mock_location_enabled`: 模拟位置是否启用
  - `last_known_location`: 最后已知位置 (如果可用)
  - `location_accuracy`: 位置精度信息
  - `detection_method`: 检测方法

## 测试和示例

### 运行测试

```bash
python test_location_functionality.py
```

### 查看使用示例

```bash
python location_usage_example.py
```

## 实现细节

### 方法1: ADB Settings (位置模式)
```bash
adb shell settings get secure location_mode
```
返回值：0=关闭, 1=仅设备, 2=省电, 3=高精度

### 方法2: ADB Settings (位置提供者)
```bash
adb shell settings get secure location_providers_allowed
```
返回允许的位置提供者列表，如: "gps,network,passive"

### 方法3: LocationManager Dumpsys
```bash
adb shell dumpsys location
```
解析输出中的位置服务详细信息，包括提供者状态、位置精度等。

### 方法4: 系统属性
```bash
adb shell getprop ro.config.location_enabled
adb shell getprop persist.vendor.location.enabled
```

## 应用场景

### 1. 应用启动检查
```python
def check_app_requirements():
    checker = SystemStatusChecker()
    if not checker.check_location_status():
        show_location_required_dialog()
```

### 2. 功能可用性检查
```python
def can_use_navigation():
    checker = SystemStatusChecker()
    detailed = checker.get_location_detailed_status()
    return detailed['gps_enabled'] or detailed['network_location_enabled']
```

### 3. 位置精度评估
```python
def get_location_accuracy_level():
    checker = SystemStatusChecker()
    detailed = checker.get_location_detailed_status()
    
    if detailed['location_mode'] == 3:
        return "高精度"
    elif detailed['location_mode'] == 2:
        return "省电模式"
    elif detailed['location_mode'] == 1:
        return "仅GPS"
    else:
        return "位置服务关闭"
```

### 4. 模拟位置检测
```python
def detect_mock_location():
    checker = SystemStatusChecker()
    detailed = checker.get_location_detailed_status()
    
    if detailed['mock_location_enabled']:
        log.warning("检测到模拟位置，某些功能可能受限")
        return True
    return False
```

## 错误处理

- 所有方法都包含异常处理
- 如果一种方法失败，会自动尝试下一种方法
- 返回 False 表示位置服务关闭或检测失败
- 详细的日志记录帮助调试问题

## 兼容性

- 支持不同Android版本的位置服务系统
- 兼容不同厂商的位置服务实现
- 自动适配不同的位置模式设置
- 支持各种位置提供者组合

## 隐私和安全考虑

1. **权限要求**: 需要ADB连接和相应权限
2. **隐私保护**: 不会获取实际位置坐标，仅检查服务状态
3. **模拟位置检测**: 可以检测开发者模拟位置设置
4. **数据安全**: 所有检测都在本地进行，不涉及网络传输

## 注意事项

1. 需要ADB连接和相应权限
2. 某些系统可能限制位置信息访问
3. dumpsys location 在某些设备上可能需要更高权限
4. 位置模式值可能因Android版本而异
5. 模拟位置检测可能在某些设备上不准确

## 故障排除

### 常见问题

1. **获取位置模式失败**
   - 检查ADB连接
   - 确认设备已解锁
   - 尝试重新连接ADB

2. **dumpsys location 超时**
   - 某些设备上该命令响应较慢
   - 已设置15秒超时，通常足够

3. **检测结果不准确**
   - 不同Android版本可能有差异
   - 建议结合多种检测方法的结果

### 调试建议

1. 启用详细日志查看检测过程
2. 运行测试脚本验证功能
3. 检查设备的位置服务设置
4. 对比手动检查和自动检测的结果

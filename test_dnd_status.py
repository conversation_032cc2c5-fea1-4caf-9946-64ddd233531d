#!/usr/bin/env python3
"""
Do Not Disturb状态检查测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


def test_dnd_basic_status():
    """测试基本的Do Not Disturb状态检查"""
    print("=" * 50)
    print("测试基本Do Not Disturb状态检查")
    print("=" * 50)
    
    checker = SystemStatusChecker()
    
    try:
        # 测试基本状态
        dnd_status = checker.check_do_not_disturb_status()
        print(f"Do Not Disturb状态: {'开启' if dnd_status else '关闭'}")
        
        return dnd_status
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False


def test_dnd_detailed_status():
    """测试详细的Do Not Disturb状态信息"""
    print("\n" + "=" * 50)
    print("测试详细Do Not Disturb状态信息")
    print("=" * 50)
    
    checker = SystemStatusChecker()
    
    try:
        # 测试详细状态
        dnd_info = checker.get_do_not_disturb_detailed_status()
        
        print(f"DND启用状态: {dnd_info['dnd_enabled']}")
        print(f"Zen模式: {dnd_info['zen_mode']} ({dnd_info['zen_mode_description']})")
        print(f"检测方法: {dnd_info['detection_method']}")
        
        if dnd_info['priority_categories']:
            print(f"优先级类别: {', '.join(dnd_info['priority_categories'])}")
        
        if dnd_info['allowed_callers']:
            print(f"允许的来电者: {dnd_info['allowed_callers']}")
            
        if dnd_info['allowed_messages']:
            print(f"允许的消息发送者: {dnd_info['allowed_messages']}")
            
        if dnd_info['schedule_enabled']:
            print(f"计划启用: {dnd_info['schedule_enabled']}")
            if dnd_info['schedule_info']:
                print(f"计划信息: {dnd_info['schedule_info']}")
        
        return dnd_info
        
    except Exception as e:
        print(f"测试失败: {e}")
        return None


def test_all_system_status():
    """测试所有系统状态（包括新的DND功能）"""
    print("\n" + "=" * 50)
    print("测试所有系统状态")
    print("=" * 50)
    
    checker = SystemStatusChecker()
    
    status_methods = [
        ("蓝牙", checker.check_bluetooth_status),
        ("WiFi", checker.check_wifi_status),
        ("手电筒", checker.check_flashlight_status),
        ("Do Not Disturb", checker.check_do_not_disturb_status),
    ]
    
    results = {}
    
    for name, method in status_methods:
        try:
            status = method()
            results[name] = status
            print(f"{name}状态: {'开启' if status else '关闭'}")
        except Exception as e:
            results[name] = None
            print(f"{name}状态检查失败: {e}")
    
    return results


def main():
    """主测试函数"""
    print("Do Not Disturb状态检查功能测试")
    print("=" * 60)
    
    # 测试基本状态
    basic_status = test_dnd_basic_status()
    
    # 测试详细状态
    detailed_status = test_dnd_detailed_status()
    
    # 测试所有系统状态
    all_status = test_all_system_status()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"基本DND状态测试: {'通过' if basic_status is not None else '失败'}")
    print(f"详细DND状态测试: {'通过' if detailed_status is not None else '失败'}")
    print(f"所有系统状态测试: {'通过' if all_status else '失败'}")
    
    if basic_status is not None:
        print(f"\n当前Do Not Disturb状态: {'开启' if basic_status else '关闭'}")
        
        if detailed_status and detailed_status['zen_mode_description'] != '错误':
            print(f"详细模式: {detailed_status['zen_mode_description']}")


if __name__ == "__main__":
    main()

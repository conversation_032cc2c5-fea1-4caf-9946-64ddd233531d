#!/usr/bin/env python3
"""
移动数据状态检查功能使用示例

本示例展示如何使用SystemStatusChecker类来检查Android设备的移动数据状态
"""

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


def example_basic_mobile_data_check():
    """基本移动数据状态检查示例"""
    print("=" * 50)
    print("基本移动数据状态检查")
    print("=" * 50)
    
    # 创建状态检查器
    checker = SystemStatusChecker()
    
    # 检查移动数据是否开启
    is_enabled = checker.check_mobile_data_status()
    
    if is_enabled:
        print("✅ 移动数据已开启")
    else:
        print("❌ 移动数据已关闭")
    
    return is_enabled


def example_detailed_mobile_data_check():
    """详细移动数据状态检查示例"""
    print("\n" + "=" * 50)
    print("详细移动数据状态检查")
    print("=" * 50)
    
    # 创建状态检查器
    checker = SystemStatusChecker()
    
    # 获取详细的移动数据状态信息
    status_info = checker.check_mobile_data_connection_status()
    
    print("移动数据详细状态:")
    print(f"  📱 移动数据开启: {'是' if status_info['mobile_data_enabled'] else '否'}")
    print(f"  🔄 始终开启: {'是' if status_info['mobile_data_always_on'] else '否'}")
    print(f"  🌐 连接状态: {'已连接' if status_info['connected'] else '未连接'}")
    
    if status_info['network_type']:
        print(f"  📶 网络类型: {status_info['network_type']}")
    
    if status_info['operator_name']:
        print(f"  📡 运营商: {status_info['operator_name']}")
    
    if status_info['signal_strength']:
        print(f"  📊 信号强度: {status_info['signal_strength']} dBm")
    
    return status_info


def example_conditional_logic():
    """基于移动数据状态的条件逻辑示例"""
    print("\n" + "=" * 50)
    print("条件逻辑示例")
    print("=" * 50)
    
    checker = SystemStatusChecker()
    
    # 获取移动数据状态
    mobile_data_info = checker.check_mobile_data_connection_status()
    
    if not mobile_data_info['mobile_data_enabled']:
        print("⚠️  移动数据未开启，某些功能可能无法使用")
        print("💡 建议：请在设置中开启移动数据")
        
    elif mobile_data_info['mobile_data_enabled'] and not mobile_data_info['connected']:
        print("⚠️  移动数据已开启但未连接")
        print("💡 建议：检查网络信号或联系运营商")
        
    elif mobile_data_info['connected']:
        print("✅ 移动数据工作正常")
        
        # 根据网络类型给出建议
        network_type = mobile_data_info.get('network_type')
        if network_type:
            if 'LTE' in network_type or 'NR' in network_type:
                print("🚀 您正在使用高速网络")
            elif network_type in ['GPRS', 'EDGE', '2G']:
                print("🐌 您正在使用较慢的网络，建议切换到更好的信号区域")
        
        # 根据信号强度给出建议
        signal_strength = mobile_data_info.get('signal_strength')
        if signal_strength:
            if signal_strength > -70:
                print("📶 信号强度优秀")
            elif signal_strength > -85:
                print("📶 信号强度良好")
            elif signal_strength > -100:
                print("📶 信号强度一般")
            else:
                print("📶 信号强度较弱，可能影响网络速度")


def main():
    """主函数 - 运行所有示例"""
    try:
        # 基本检查
        example_basic_mobile_data_check()
        
        # 详细检查
        example_detailed_mobile_data_check()
        
        # 条件逻辑
        example_conditional_logic()
        
        print("\n" + "=" * 50)
        print("✅ 所有示例运行完成")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ 运行示例时发生错误: {e}")
        log.error(f"移动数据状态检查示例失败: {e}")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试屏幕亮度功能
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log

def test_brightness_functions():
    """测试屏幕亮度相关功能"""
    print("=" * 50)
    print("测试屏幕亮度功能")
    print("=" * 50)
    
    # 创建状态检查器实例
    checker = SystemStatusChecker()
    
    # 测试基本亮度获取
    print("\n1. 测试基本亮度获取:")
    brightness = checker.get_screen_brightness()
    print(f"屏幕亮度值: {brightness}")
    
    if brightness >= 0:
        percentage = (brightness / 255) * 100
        print(f"亮度百分比: {percentage:.1f}%")
    else:
        print("获取亮度失败")
    
    # 测试详细亮度状态
    print("\n2. 测试详细亮度状态:")
    detailed_status = checker.get_screen_brightness_detailed_status()
    
    print("详细亮度信息:")
    for key, value in detailed_status.items():
        print(f"  {key}: {value}")
    
    # 分析结果
    print("\n3. 结果分析:")
    if detailed_status['brightness_value'] >= 0:
        print(f"✅ 成功获取亮度值: {detailed_status['brightness_value']}/255")
        print(f"✅ 亮度百分比: {detailed_status['brightness_percentage']:.1f}%")
    else:
        print("❌ 获取亮度值失败")
    
    if detailed_status['auto_brightness_enabled']:
        print("✅ 自动亮度已开启")
    else:
        print("ℹ️  自动亮度已关闭")
    
    if detailed_status['adaptive_brightness_enabled']:
        print("✅ 自适应亮度已开启")
    else:
        print("ℹ️  自适应亮度已关闭")
    
    if detailed_status['detection_method']:
        print(f"✅ 检测方法: {detailed_status['detection_method']}")
    
    if detailed_status['brightness_path_found']:
        print(f"✅ 硬件路径: {detailed_status['brightness_path_found']}")
        if detailed_status['hardware_brightness'] >= 0:
            print(f"✅ 硬件亮度: {detailed_status['hardware_brightness']}")
        if detailed_status['hardware_max_brightness'] >= 0:
            print(f"✅ 硬件最大亮度: {detailed_status['hardware_max_brightness']}")

if __name__ == "__main__":
    test_brightness_functions()

#!/usr/bin/env python3
"""
测试音量范围和百分比计算功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


def test_volume_range_and_percentage():
    """测试音量范围和百分比计算"""
    
    print("=" * 70)
    print("测试音量范围和百分比计算功能")
    print("=" * 70)
    
    checker = SystemStatusChecker()
    
    # 测试各种音量类型的范围和百分比
    volume_types = ["music", "ring", "alarm", "notification", "system", "voice_call"]
    
    print("\n1. 测试各音量类型的最大值获取:")
    print("-" * 50)
    
    max_volumes = {}
    for vol_type in volume_types:
        try:
            max_vol = checker.get_volume_max_value(vol_type)
            max_volumes[vol_type] = max_vol
            if max_vol > 0:
                print(f"✅ {vol_type:12} 最大音量: {max_vol}")
            else:
                print(f"❌ {vol_type:12} 最大音量: 获取失败")
        except Exception as e:
            print(f"❌ {vol_type:12} 最大音量: 异常 - {e}")
            max_volumes[vol_type] = -1
    
    print("\n2. 测试当前音量及百分比:")
    print("-" * 50)
    
    for vol_type in volume_types:
        try:
            volume_info = checker.get_volume_with_percentage(vol_type)
            
            current = volume_info['current_volume']
            max_vol = volume_info['max_volume']
            percentage = volume_info['percentage']
            is_max = volume_info['is_max']
            method = volume_info['detection_method']
            
            if current >= 0:
                status_line = f"✅ {vol_type:12}: {current}"
                if max_vol > 0:
                    status_line += f"/{max_vol} ({percentage}%)"
                    if is_max:
                        status_line += " [最大音量]"
                else:
                    status_line += " [无法获取最大值]"
                status_line += f" [{method}]"
                print(status_line)
            else:
                print(f"❌ {vol_type:12}: 获取失败")
                
        except Exception as e:
            print(f"❌ {vol_type:12}: 异常 - {e}")
    
    print("\n3. 专门测试媒体音量 (详细分析):")
    print("-" * 50)
    
    try:
        # 获取媒体音量的详细信息
        music_info = checker.get_volume_with_percentage("music")
        
        print(f"当前媒体音量: {music_info['current_volume']}")
        print(f"最大媒体音量: {music_info['max_volume']}")
        print(f"音量百分比: {music_info['percentage']}%")
        print(f"是否最大音量: {'是' if music_info['is_max'] else '否'}")
        print(f"检测方法: {music_info['detection_method']}")
        
        # 分析音量状态
        current = music_info['current_volume']
        percentage = music_info['percentage']
        
        if current > 0:
            if music_info['is_max']:
                print("🔊 状态分析: 媒体音量已调至最大")
            elif percentage >= 80:
                print("📢 状态分析: 媒体音量很高")
            elif percentage >= 50:
                print("🔉 状态分析: 媒体音量中等")
            elif percentage >= 20:
                print("🔈 状态分析: 媒体音量较低")
            else:
                print("🔇 状态分析: 媒体音量很低")
        else:
            print("❌ 状态分析: 无法获取媒体音量")
            
    except Exception as e:
        print(f"❌ 媒体音量详细分析失败: {e}")
    
    print("\n4. 音量范围验证:")
    print("-" * 50)
    
    # 验证音量范围的合理性
    for vol_type in volume_types:
        try:
            current = checker.get_system_volume(vol_type)
            max_vol = max_volumes.get(vol_type, -1)
            
            if current >= 0 and max_vol > 0:
                if current <= max_vol:
                    ratio = (current / max_vol) * 100
                    print(f"✅ {vol_type:12}: {current}/{max_vol} ({ratio:.1f}%) - 范围正常")
                else:
                    print(f"⚠️ {vol_type:12}: {current}/{max_vol} - 当前值超过最大值!")
            elif current >= 0:
                print(f"❓ {vol_type:12}: {current}/? - 无法确定最大值")
            else:
                print(f"❌ {vol_type:12}: 无法获取当前值")
                
        except Exception as e:
            print(f"❌ {vol_type:12}: 验证失败 - {e}")
    
    print("\n5. 建议的音量显示方式:")
    print("-" * 50)
    
    try:
        music_info = checker.get_volume_with_percentage("music")
        current = music_info['current_volume']
        max_vol = music_info['max_volume']
        percentage = music_info['percentage']
        
        if current >= 0 and max_vol > 0:
            print(f"📱 推荐显示格式:")
            print(f"   简单格式: {percentage:.0f}%")
            print(f"   详细格式: {current}/{max_vol} ({percentage:.1f}%)")
            print(f"   进度条格式: {'█' * int(percentage/10)}{'░' * (10-int(percentage/10))} {percentage:.0f}%")
            
            # 根据百分比给出建议
            if music_info['is_max']:
                print(f"💡 提示: 媒体音量已达到最大值 ({current})")
            elif percentage < 50:
                print(f"💡 提示: 媒体音量较低，可能需要调高")
            else:
                print(f"💡 提示: 媒体音量正常")
        else:
            print("❌ 无法生成推荐显示格式")
            
    except Exception as e:
        print(f"❌ 生成显示格式失败: {e}")
    
    print("\n" + "=" * 70)
    print("测试完成")
    print("=" * 70)


def analyze_volume_issue():
    """分析音量显示问题"""
    
    print("\n" + "=" * 70)
    print("分析音量显示问题")
    print("=" * 70)
    
    checker = SystemStatusChecker()
    
    print("\n问题分析: 为什么音量调到最大但显示只有13?")
    print("-" * 50)
    
    try:
        # 获取详细的音量信息
        music_info = checker.get_volume_with_percentage("music")
        
        current = music_info['current_volume']
        max_vol = music_info['max_volume']
        percentage = music_info['percentage']
        is_max = music_info['is_max']
        
        print(f"当前检测结果:")
        print(f"  当前音量值: {current}")
        print(f"  最大音量值: {max_vol}")
        print(f"  计算百分比: {percentage}%")
        print(f"  是否最大音量: {'是' if is_max else '否'}")
        
        print(f"\n原因分析:")
        if current == 13 and max_vol > 13:
            print(f"✅ 正常情况: 系统最大音量为{max_vol}，当前设置为{current}")
            print(f"   这意味着您的音量实际上是 {percentage:.1f}%，而不是100%")
            print(f"   如果要达到100%，需要将音量调整到{max_vol}")
        elif current == 13 and max_vol == 13:
            print(f"✅ 已达最大: 系统最大音量就是{max_vol}，当前已是最大音量")
        elif current == 13 and max_vol < 0:
            print(f"⚠️ 无法确定: 无法获取最大音量值，可能{current}就是最大值")
        else:
            print(f"❓ 其他情况: 当前{current}，最大{max_vol}")
        
        print(f"\n解决方案:")
        if not is_max and max_vol > current:
            print(f"🔧 建议: 可以继续调高音量到{max_vol}以达到真正的最大音量")
        else:
            print(f"✅ 确认: 当前音量已经是系统允许的最大值")
            
        print(f"\n显示建议:")
        print(f"📊 推荐显示: {percentage:.0f}% (而不是显示原始值{current})")
        print(f"📊 或者显示: {current}/{max_vol}")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")


if __name__ == "__main__":
    test_volume_range_and_percentage()
    analyze_volume_issue()

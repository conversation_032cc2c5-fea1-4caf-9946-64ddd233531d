#!/usr/bin/env python3
"""
测试当前系统主题状态
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatus<PERSON>he<PERSON>

def test_current_theme():
    """测试当前主题状态"""
    checker = SystemStatusChecker()
    
    # 测试主题状态
    is_light = checker.check_light_theme_status()
    print(f"当前系统主题: {'浅色主题' if is_light else '深色主题'}")
    
    # 获取详细主题信息
    theme_info = checker.get_light_theme_detailed_status()
    print(f"主题模式: {theme_info['theme_mode']}")
    print(f"UI夜间模式: {theme_info['ui_night_mode_description']}")
    if theme_info['auto_mode_enabled']:
        print(f"自动模式当前状态: {theme_info['auto_mode_current_status']}")
    print(f"检测方法: {theme_info['detection_method']}")

if __name__ == "__main__":
    test_current_theme()

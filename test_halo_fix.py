#!/usr/bin/env python3
"""
测试修复后的 Active Halo Lighting 检测
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatus<PERSON>he<PERSON>

def test_fixed_halo_detection():
    """测试修复后的检测逻辑"""
    print("=" * 50)
    print("测试修复后的 Active Halo Lighting 检测")
    print("=" * 50)
    
    checker = SystemStatusChecker()
    
    print("\n1. 检测 Active Halo Lighting 状态:")
    print("-" * 30)
    result = checker.check_active_halo_lighting_status()
    print(f"检测结果: {result}")
    print(f"预期结果: False (TECNO设备不支持Active Halo Lighting)")
    print(f"检测是否正确: {'✅' if result == False else '❌'}")
    
    print("\n2. 获取详细状态信息:")
    print("-" * 30)
    detailed_status = checker.get_active_halo_lighting_detailed_status()
    print(f"Halo Lighting 开启: {detailed_status['halo_lighting_enabled']}")
    print(f"检测方法: {detailed_status['detection_method']}")
    
    return result == False

if __name__ == "__main__":
    success = test_fixed_halo_detection()
    if success:
        print("\n✅ 修复成功！检测结果正确")
    else:
        print("\n❌ 修复失败！检测结果仍然不正确")

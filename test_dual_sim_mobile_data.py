#!/usr/bin/env python3
"""
测试双卡移动数据状态检查功能
"""

from pages.base.system_status_checker import SystemStatusChecker

def test_dual_sim_mobile_data():
    print('=' * 60)
    print('双卡移动数据状态检查 - 优化验证')
    print('=' * 60)

    checker = SystemStatusChecker()

    # 基本检查
    print('\n1. 基本移动数据状态检查:')
    result = checker.check_mobile_data_status()
    print(f'结果: {result}')

    # 详细检查
    print('\n2. 双卡详细状态信息:')
    detail = checker.check_mobile_data_connection_status()
    print(f'移动数据启用: {detail["mobile_data_enabled"]}')
    print(f'SIM卡状态: {detail["sim_state"]}')
    print(f'活跃数据SIM: {detail["active_data_sim"]}')
    
    print('\n双卡详细信息:')
    for sim_key, sim_info in detail['dual_sim_info'].items():
        print(f'  {sim_key}: 状态={sim_info["state"]}, 可用={sim_info["available"]}, 运营商={sim_info["operator"]}')

    print('\n' + '=' * 60)
    print('双卡优化验证完成')
    print('=' * 60)

if __name__ == "__main__":
    test_dual_sim_mobile_data()

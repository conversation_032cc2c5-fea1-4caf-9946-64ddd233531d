#!/usr/bin/env python3
"""
详细调试 Active Halo Lighting 设置
"""

import subprocess
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_all_halo_settings():
    """调试所有可能的 Halo Lighting 相关设置"""
    print("=" * 60)
    print("详细调试 Active Halo Lighting 设置")
    print("=" * 60)
    
    # 1. 检查当前通知LED脉冲设置
    print("\n1. 通知LED脉冲设置:")
    print("-" * 30)
    try:
        result = subprocess.run(
            ["adb", "shell", "settings", "get", "system", "notification_light_pulse"],
            capture_output=True,
            text=True,
            timeout=5
        )
        if result.returncode == 0:
            print(f"notification_light_pulse: {result.stdout.strip()}")
        else:
            print(f"获取失败: {result.stderr}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 2. 检查所有可能的 halo lighting 相关设置
    print("\n2. 检查所有可能的 Halo Lighting 设置:")
    print("-" * 30)
    
    halo_settings = [
        ("system", "halo_lighting_enabled"),
        ("system", "active_halo_lighting"),
        ("system", "notification_halo_light"),
        ("system", "halo_light_enable"),
        ("system", "led_notification_enabled"),
        ("secure", "halo_lighting_enabled"),
        ("secure", "active_halo_lighting_enabled"),
        ("secure", "notification_led_enabled"),
        ("global", "halo_lighting"),
        ("global", "active_halo_lighting")
    ]
    
    for namespace, setting in halo_settings:
        try:
            result = subprocess.run(
                ["adb", "shell", "settings", "get", namespace, setting],
                capture_output=True,
                text=True,
                timeout=3
            )
            if result.returncode == 0:
                value = result.stdout.strip()
                if value and value != "null":
                    print(f"{namespace}.{setting}: {value}")
                else:
                    print(f"{namespace}.{setting}: (未设置)")
        except:
            print(f"{namespace}.{setting}: (获取失败)")
    
    # 3. 检查系统属性
    print("\n3. 检查 Halo Lighting 相关系统属性:")
    print("-" * 30)
    
    halo_properties = [
        "persist.vendor.halo.lighting",
        "ro.vendor.halo_lighting.support",
        "persist.sys.halo_light",
        "vendor.halo.lighting.enable",
        "ro.config.notification_led",
        "persist.vendor.notification.led",
        "ro.vendor.infinix.halo",
        "persist.infinix.halo.lighting"
    ]
    
    for prop in halo_properties:
        try:
            result = subprocess.run(
                ["adb", "shell", "getprop", prop],
                capture_output=True,
                text=True,
                timeout=3
            )
            if result.returncode == 0:
                value = result.stdout.strip()
                if value and value not in ["", "null"]:
                    print(f"{prop}: {value}")
                else:
                    print(f"{prop}: (未设置)")
        except:
            print(f"{prop}: (获取失败)")
    
    # 4. 搜索所有包含 "halo" 的设置
    print("\n4. 搜索所有包含 'halo' 的设置:")
    print("-" * 30)
    
    for namespace in ["system", "secure", "global"]:
        try:
            result = subprocess.run(
                ["adb", "shell", "settings", "list", namespace],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                settings = result.stdout.split('\n')
                halo_settings = [s for s in settings if 'halo' in s.lower()]
                
                if halo_settings:
                    print(f"\n{namespace} 命名空间中的 halo 相关设置:")
                    for setting in halo_settings:
                        print(f"  {setting}")
        except Exception as e:
            print(f"搜索 {namespace} 失败: {e}")
    
    # 5. 检查设备信息
    print("\n5. 设备信息:")
    print("-" * 30)
    
    device_props = [
        "ro.product.brand",
        "ro.product.model",
        "ro.product.name",
        "ro.build.version.release"
    ]
    
    for prop in device_props:
        try:
            result = subprocess.run(
                ["adb", "shell", "getprop", prop],
                capture_output=True,
                text=True,
                timeout=3
            )
            if result.returncode == 0:
                value = result.stdout.strip()
                print(f"{prop}: {value}")
        except:
            print(f"{prop}: (获取失败)")

if __name__ == "__main__":
    debug_all_halo_settings()

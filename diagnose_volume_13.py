#!/usr/bin/env python3
"""
诊断音量显示13的问题
"""

import sys
import os
import subprocess

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


def diagnose_volume_13_issue():
    """诊断音量显示13的具体问题"""
    
    print("=" * 80)
    print("🔍 诊断音量显示13的问题")
    print("=" * 80)
    
    checker = SystemStatusChecker()
    
    print("\n📋 步骤1: 获取原始音量信息")
    print("-" * 50)
    
    try:
        # 直接通过ADB获取音量设置
        result = subprocess.run(
            ["adb", "shell", "settings", "get", "system", "volume_music"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            raw_volume = result.stdout.strip()
            print(f"✅ ADB获取的原始音量值: {raw_volume}")
        else:
            print(f"❌ ADB获取音量失败: {result.stderr}")
            raw_volume = "获取失败"
    except Exception as e:
        print(f"❌ ADB命令异常: {e}")
        raw_volume = "异常"
    
    print("\n📋 步骤2: 通过我们的方法获取音量")
    print("-" * 50)
    
    try:
        our_volume = checker.get_system_volume("music")
        print(f"✅ 我们方法获取的音量: {our_volume}")
    except Exception as e:
        print(f"❌ 我们方法获取失败: {e}")
        our_volume = -1
    
    print("\n📋 步骤3: 获取音量最大值")
    print("-" * 50)
    
    try:
        max_volume = checker.get_volume_max_value("music")
        print(f"✅ 检测到的最大音量: {max_volume}")
    except Exception as e:
        print(f"❌ 获取最大音量失败: {e}")
        max_volume = -1
    
    print("\n📋 步骤4: 通过dumpsys audio分析")
    print("-" * 50)
    
    try:
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "audio"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            audio_output = result.stdout
            print("✅ dumpsys audio 执行成功")
            
            # 查找STREAM_MUSIC相关信息
            lines = audio_output.split('\n')
            music_lines = [line for line in lines if 'STREAM_MUSIC' in line]
            
            print(f"📄 找到 {len(music_lines)} 行包含STREAM_MUSIC的信息:")
            for i, line in enumerate(music_lines[:5]):  # 只显示前5行
                print(f"   {i+1}. {line.strip()}")
            
            # 查找音量相关的数字
            import re
            volume_numbers = []
            for line in music_lines:
                numbers = re.findall(r'\d+', line)
                if numbers:
                    volume_numbers.extend(numbers)
            
            if volume_numbers:
                print(f"🔢 在STREAM_MUSIC行中找到的数字: {volume_numbers[:10]}")  # 显示前10个数字
            else:
                print("❌ 未在STREAM_MUSIC行中找到数字")
                
        else:
            print(f"❌ dumpsys audio 失败: {result.stderr}")
    except Exception as e:
        print(f"❌ dumpsys audio 异常: {e}")
    
    print("\n📋 步骤5: 计算正确的百分比")
    print("-" * 50)
    
    try:
        volume_info = checker.get_volume_with_percentage("music")
        
        current = volume_info['current_volume']
        max_vol = volume_info['max_volume']
        percentage = volume_info['percentage']
        is_max = volume_info['is_max']
        
        print(f"📊 完整音量信息:")
        print(f"   当前音量: {current}")
        print(f"   最大音量: {max_vol}")
        print(f"   百分比: {percentage:.1f}%")
        print(f"   是否最大: {'是' if is_max else '否'}")
        
    except Exception as e:
        print(f"❌ 计算百分比失败: {e}")
    
    print("\n📋 步骤6: 问题分析和解决方案")
    print("-" * 50)
    
    # 分析问题
    if raw_volume == "13" and our_volume == 13:
        print("✅ 数据一致性: ADB和我们的方法获取的值一致")
        
        if max_volume > 13:
            print(f"🎯 问题确认: 音量13不是最大值!")
            print(f"   • 系统最大音量是: {max_volume}")
            print(f"   • 当前音量13相当于: {(13/max_volume)*100:.1f}%")
            print(f"   • 要达到100%需要调整到: {max_volume}")
            
            print(f"\n💡 解决方案:")
            print(f"   1. 在UI中显示百分比: {(13/max_volume)*100:.0f}% (而不是13)")
            print(f"   2. 或显示分数格式: 13/{max_volume}")
            print(f"   3. 提示用户可以继续调高到{max_volume}")
            
        elif max_volume == 13:
            print(f"✅ 确认: 音量13就是最大值")
            print(f"   • 系统最大音量确实是13")
            print(f"   • 当前已达到100%音量")
            
        else:
            print(f"❓ 无法确定最大音量，需要进一步调试")
            
    else:
        print(f"⚠️ 数据不一致:")
        print(f"   ADB获取: {raw_volume}")
        print(f"   我们方法: {our_volume}")
        print(f"   需要检查方法实现")
    
    print("\n📋 步骤7: 推荐的实现方式")
    print("-" * 50)
    
    print("🔧 推荐代码:")
    print("""
# 获取音量百分比的正确方式
checker = SystemStatusChecker()
volume_info = checker.get_volume_with_percentage("music")

# 显示给用户的值
display_value = f"{volume_info['percentage']:.0f}%"
print(f"媒体音量: {display_value}")

# 如果需要显示详细信息
if volume_info['current_volume'] > 0:
    detail = f"{volume_info['current_volume']}/{volume_info['max_volume']}"
    print(f"详细信息: {detail}")
""")
    
    print("\n🎯 关键要点:")
    print("   • 不要直接显示原始值13")
    print("   • 计算并显示百分比")
    print("   • 获取最大音量值用于计算")
    print("   • 提供用户友好的显示格式")


def quick_volume_check():
    """快速检查当前音量状态"""
    
    print("\n" + "=" * 80)
    print("🚀 快速音量状态检查")
    print("=" * 80)
    
    checker = SystemStatusChecker()
    
    try:
        volume_info = checker.get_volume_with_percentage("music")
        
        print(f"\n📱 当前媒体音量状态:")
        print(f"   原始值: {volume_info['current_volume']}")
        print(f"   最大值: {volume_info['max_volume']}")
        print(f"   百分比: {volume_info['percentage']:.1f}%")
        print(f"   建议显示: {volume_info['percentage']:.0f}%")
        
        if volume_info['is_max']:
            print(f"   🔊 状态: 已达最大音量")
        else:
            remaining = volume_info['max_volume'] - volume_info['current_volume']
            print(f"   📈 状态: 还可以调高 {remaining} 级")
            
    except Exception as e:
        print(f"❌ 快速检查失败: {e}")


if __name__ == "__main__":
    diagnose_volume_13_issue()
    quick_volume_check()

#!/usr/bin/env python3
"""
测试位置服务状态获取功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


def test_location_functions():
    """测试位置服务获取功能"""
    
    print("=" * 70)
    print("测试位置服务状态获取功能")
    print("=" * 70)
    
    # 创建系统状态检查器实例
    checker = SystemStatusChecker()
    
    print("\n1. 测试基本位置服务状态获取:")
    print("-" * 50)
    
    try:
        location_enabled = checker.check_location_status()
        if location_enabled:
            print("✅ 位置服务状态: 开启")
        else:
            print("❌ 位置服务状态: 关闭")
    except Exception as e:
        print(f"❌ 位置服务状态获取异常: {e}")
    
    print("\n2. 测试详细位置服务状态获取:")
    print("-" * 50)
    
    try:
        detailed_status = checker.get_location_detailed_status()
        
        print(f"检测方法: {detailed_status.get('detection_method', 'Unknown')}")
        print(f"位置服务启用: {'是' if detailed_status.get('location_enabled', False) else '否'}")
        print(f"位置模式: {detailed_status.get('location_mode_description', 'Unknown')}")
        print(f"位置模式值: {detailed_status.get('location_mode', -1)}")
        
        # 显示位置提供者信息
        providers_allowed = detailed_status.get('providers_allowed', [])
        if providers_allowed:
            print(f"允许的提供者: {', '.join(providers_allowed)}")
        else:
            print("允许的提供者: 无")
        
        active_providers = detailed_status.get('active_providers', [])
        if active_providers:
            print(f"活跃的提供者: {', '.join(active_providers)}")
        else:
            print("活跃的提供者: 无")
        
        # 显示具体提供者状态
        print(f"GPS启用: {'是' if detailed_status.get('gps_enabled', False) else '否'}")
        print(f"网络定位启用: {'是' if detailed_status.get('network_location_enabled', False) else '否'}")
        print(f"被动定位启用: {'是' if detailed_status.get('passive_location_enabled', False) else '否'}")
        print(f"模拟位置启用: {'是' if detailed_status.get('mock_location_enabled', False) else '否'}")
        
        # 显示位置信息
        last_location = detailed_status.get('last_known_location')
        if last_location:
            print(f"最后已知位置: 纬度 {last_location.get('latitude', 'N/A')}, 经度 {last_location.get('longitude', 'N/A')}")
        else:
            print("最后已知位置: 无")
        
        accuracy = detailed_status.get('location_accuracy')
        if accuracy:
            print(f"位置精度: {accuracy}")
        else:
            print("位置精度: 未知")
            
    except Exception as e:
        print(f"❌ 获取详细位置服务状态失败: {e}")
    
    print("\n3. 位置服务状态分析:")
    print("-" * 50)
    
    try:
        detailed_status = checker.get_location_detailed_status()
        
        location_enabled = detailed_status.get('location_enabled', False)
        location_mode = detailed_status.get('location_mode', 0)
        gps_enabled = detailed_status.get('gps_enabled', False)
        network_enabled = detailed_status.get('network_location_enabled', False)
        mock_enabled = detailed_status.get('mock_location_enabled', False)
        
        print("📍 位置服务分析:")
        
        if not location_enabled:
            print("   🔴 位置服务已关闭")
            print("   💡 建议: 在设置中开启位置服务以获得更好的应用体验")
        else:
            print("   🟢 位置服务已开启")
            
            if location_mode == 3:
                print("   🎯 高精度模式: GPS + 网络定位")
                print("   💡 优点: 定位最准确，但耗电较多")
            elif location_mode == 2:
                print("   🔋 省电模式: 仅网络定位")
                print("   💡 优点: 省电，但精度可能较低")
            elif location_mode == 1:
                print("   🛰️ 仅设备模式: 仅GPS定位")
                print("   💡 优点: 不依赖网络，但室内可能无法定位")
            else:
                print("   ❓ 未知定位模式")
        
        if gps_enabled:
            print("   🛰️ GPS定位: 已启用")
        else:
            print("   📡 GPS定位: 已禁用")
        
        if network_enabled:
            print("   🌐 网络定位: 已启用")
        else:
            print("   📶 网络定位: 已禁用")
        
        if mock_enabled:
            print("   ⚠️ 模拟位置: 已启用 (开发者选项)")
            print("   💡 注意: 某些应用可能检测到模拟位置并拒绝服务")
        else:
            print("   ✅ 模拟位置: 已禁用")
        
        # 给出建议
        print("\n📋 使用建议:")
        if not location_enabled:
            print("   • 开启位置服务以使用导航、天气等功能")
        elif location_mode == 0:
            print("   • 当前位置服务已关闭")
        elif location_mode == 1:
            print("   • 适合户外使用，室内建议开启网络定位")
        elif location_mode == 2:
            print("   • 适合日常使用，需要高精度时可切换到高精度模式")
        elif location_mode == 3:
            print("   • 当前为最佳定位模式，适合导航等高精度需求")
        
    except Exception as e:
        print(f"❌ 位置服务分析失败: {e}")
    
    print("\n" + "=" * 70)
    print("测试完成")
    print("=" * 70)


def test_location_modes():
    """测试不同位置模式的识别"""
    
    print("\n" + "=" * 70)
    print("位置模式说明")
    print("=" * 70)
    
    print("\n📍 Android位置模式说明:")
    print("   0 - 关闭: 位置服务完全关闭")
    print("   1 - 仅设备: 仅使用GPS，不使用网络")
    print("   2 - 省电模式: 使用网络定位，不使用GPS")
    print("   3 - 高精度: 同时使用GPS和网络定位")
    
    print("\n🔧 检测方法说明:")
    print("   • settings get secure location_mode - 获取位置模式")
    print("   • settings get secure location_providers_allowed - 获取允许的提供者")
    print("   • dumpsys location - 获取详细位置服务信息")
    print("   • getprop - 获取位置相关系统属性")
    
    checker = SystemStatusChecker()
    
    try:
        detailed_status = checker.get_location_detailed_status()
        current_mode = detailed_status.get('location_mode', -1)
        
        print(f"\n📱 当前设备位置模式: {current_mode}")
        print(f"   描述: {detailed_status.get('location_mode_description', 'Unknown')}")
        
        if current_mode >= 0:
            print(f"   状态: {'✅ 正常' if current_mode <= 3 else '❓ 异常值'}")
        else:
            print(f"   状态: ❌ 获取失败")
            
    except Exception as e:
        print(f"❌ 获取当前位置模式失败: {e}")


if __name__ == "__main__":
    test_location_functions()
    test_location_modes()

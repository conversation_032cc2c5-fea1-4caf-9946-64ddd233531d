#!/usr/bin/env python3
"""
分析 service call 的输出，找到正确的音量值
"""

import subprocess
import re

def analyze_service_call_output():
    """分析 service call 输出"""
    print("=" * 60)
    print("分析 service call audio 输出")
    print("=" * 60)
    
    # 获取 STREAM_MUSIC (3) 的音量信息
    try:
        result = subprocess.run(
            ["adb", "shell", "service", "call", "audio", "25", "i32", "3"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            output = result.stdout
            print("原始输出:")
            print(output[:500] + "..." if len(output) > 500 else output)
            
            print("\n分析十六进制值:")
            # 查找所有十六进制值
            hex_matches = re.findall(r'0x([0-9a-f]{8})', output)
            print(f"找到 {len(hex_matches)} 个十六进制值")
            
            # 转换为十进制并分析
            decimal_values = []
            for i, hex_val in enumerate(hex_matches[:20]):  # 只看前20个
                try:
                    dec_val = int(hex_val, 16)
                    decimal_values.append(dec_val)
                    if 0 <= dec_val <= 30:  # 可能的音量值
                        print(f"  位置 {i}: 0x{hex_val} = {dec_val} ⭐ (可能的音量值)")
                    elif dec_val < 1000:
                        print(f"  位置 {i}: 0x{hex_val} = {dec_val}")
                except ValueError:
                    continue
            
            print(f"\n可能的音量值: {[v for v in decimal_values if 0 <= v <= 30]}")
            
            # 查找特定模式
            print("\n查找特定模式:")
            
            # 模式1: 查找连续的小数值
            small_values = [v for v in decimal_values if 0 <= v <= 30]
            if small_values:
                print(f"小数值序列: {small_values}")
            
            # 模式2: 查找ASCII字符串附近的数值
            lines = output.split('\n')
            for i, line in enumerate(lines):
                if 'STREAM_MUSIC' in line or 'AUDIO_STREAM' in line:
                    print(f"找到音频流相关行 {i}: {line.strip()}")
                    
        else:
            print(f"service call 失败: {result.stderr}")
    except Exception as e:
        print(f"分析失败: {e}")

def test_different_streams():
    """测试不同的音频流"""
    print("\n" + "=" * 60)
    print("测试不同音频流的 service call")
    print("=" * 60)
    
    streams = {
        "VOICE_CALL": 0,
        "SYSTEM": 1, 
        "RING": 2,
        "MUSIC": 3,
        "ALARM": 4,
        "NOTIFICATION": 5
    }
    
    for stream_name, stream_id in streams.items():
        print(f"\n{stream_name} (ID: {stream_id}):")
        try:
            result = subprocess.run(
                ["adb", "shell", "service", "call", "audio", "25", "i32", str(stream_id)],
                capture_output=True,
                text=True,
                timeout=3
            )
            
            if result.returncode == 0:
                output = result.stdout
                # 查找小数值
                hex_matches = re.findall(r'0x([0-9a-f]{8})', output)
                small_values = []
                for hex_val in hex_matches[:10]:  # 只看前10个
                    try:
                        dec_val = int(hex_val, 16)
                        if 0 <= dec_val <= 30:
                            small_values.append(dec_val)
                    except ValueError:
                        continue
                
                print(f"  可能的音量值: {small_values}")
            else:
                print(f"  失败: {result.stderr}")
        except Exception as e:
            print(f"  错误: {e}")

def try_alternative_service_calls():
    """尝试其他 service call 方法"""
    print("\n" + "=" * 60)
    print("尝试其他 service call 方法")
    print("=" * 60)
    
    # 方法1: 获取最大音量
    print("1. 获取最大音量 (service call audio 26):")
    try:
        result = subprocess.run(
            ["adb", "shell", "service", "call", "audio", "26", "i32", "3"],
            capture_output=True,
            text=True,
            timeout=3
        )
        
        if result.returncode == 0:
            output = result.stdout
            hex_matches = re.findall(r'0x([0-9a-f]{8})', output)
            for hex_val in hex_matches[:5]:
                try:
                    dec_val = int(hex_val, 16)
                    if 0 <= dec_val <= 100:
                        print(f"   可能的最大音量: {dec_val}")
                except ValueError:
                    continue
    except Exception as e:
        print(f"   错误: {e}")
    
    # 方法2: 尝试其他音频相关的service call
    print("\n2. 尝试其他音频方法:")
    methods = ["24", "27", "28"]  # 其他可能的音频方法
    
    for method in methods:
        try:
            result = subprocess.run(
                ["adb", "shell", "service", "call", "audio", method, "i32", "3"],
                capture_output=True,
                text=True,
                timeout=3
            )
            
            if result.returncode == 0:
                output = result.stdout
                if "Result:" in output and len(output) < 200:
                    print(f"   方法 {method}: {output.strip()}")
        except:
            continue

if __name__ == "__main__":
    print("开始分析 service call 输出...")
    
    # 1. 分析主要输出
    analyze_service_call_output()
    
    # 2. 测试不同流
    test_different_streams()
    
    # 3. 尝试其他方法
    try_alternative_service_calls()
    
    print("\n" + "=" * 60)
    print("分析完成")
    print("=" * 60)

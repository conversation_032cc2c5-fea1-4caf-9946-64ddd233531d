#!/usr/bin/env python3
"""
测试 Active Halo Lighting 状态检查功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


def test_halo_lighting_status():
    """测试 Active Halo Lighting 状态检查"""
    print("=" * 60)
    print("测试 Active Halo Lighting 状态检查功能")
    print("=" * 60)
    
    # 创建状态检查器实例
    checker = SystemStatusChecker()
    
    try:
        # 测试基本状态检查
        print("\n1. 检查基本 Active Halo Lighting 状态:")
        print("-" * 40)
        is_enabled = checker.check_active_halo_lighting_status()
        print(f"Active Halo Lighting 是否开启: {is_enabled}")
        print(f"状态: {'开启' if is_enabled else '关闭'}")
        
        # 测试详细状态获取
        print("\n2. 获取详细 Halo Lighting 状态信息:")
        print("-" * 40)
        detailed_status = checker.get_active_halo_lighting_detailed_status()
        
        print(f"Halo Lighting 开启: {detailed_status['halo_lighting_enabled']}")
        print(f"通知LED开启: {detailed_status['notification_led_enabled']}")
        print(f"检测方法: {detailed_status['detection_method']}")
        
        # 显示系统设置
        if detailed_status['system_settings']:
            print(f"\n系统设置:")
            for setting, value in detailed_status['system_settings'].items():
                print(f"  {setting}: {value}")
        
        # 显示安全设置
        if detailed_status['secure_settings']:
            print(f"\n安全设置:")
            for setting, value in detailed_status['secure_settings'].items():
                print(f"  {setting}: {value}")
        
        # 显示系统属性
        if detailed_status['system_properties']:
            print(f"\n系统属性:")
            for prop, value in detailed_status['system_properties'].items():
                print(f"  {prop}: {value}")
        
        # 显示支持的功能
        if detailed_status['supported_features']:
            print(f"\n支持的功能:")
            for feature in detailed_status['supported_features']:
                print(f"  {feature}")
        
        print("\n" + "=" * 60)
        print("测试完成!")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        log.error(f"Active Halo Lighting 状态测试失败: {e}")
        return False


def debug_halo_lighting_settings():
    """调试 Halo Lighting 相关设置"""
    print("\n" + "=" * 60)
    print("调试 Halo Lighting 相关设置")
    print("=" * 60)
    
    import subprocess
    
    # 检查所有可能的halo lighting相关设置
    print("\n1. 检查系统设置中的相关项:")
    print("-" * 40)
    
    try:
        result = subprocess.run(
            ["adb", "shell", "settings", "list", "system"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            settings = result.stdout.split('\n')
            halo_related = [s for s in settings if any(keyword in s.lower() for keyword in ['halo', 'led', 'light', 'notification'])]
            
            if halo_related:
                for setting in halo_related[:10]:  # 显示前10个相关设置
                    print(f"  {setting}")
            else:
                print("  未找到相关设置")
    except Exception as e:
        print(f"  获取系统设置失败: {e}")
    
    # 检查安全设置
    print("\n2. 检查安全设置中的相关项:")
    print("-" * 40)
    
    try:
        result = subprocess.run(
            ["adb", "shell", "settings", "list", "secure"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            settings = result.stdout.split('\n')
            halo_related = [s for s in settings if any(keyword in s.lower() for keyword in ['halo', 'led', 'light', 'notification'])]
            
            if halo_related:
                for setting in halo_related[:10]:  # 显示前10个相关设置
                    print(f"  {setting}")
            else:
                print("  未找到相关设置")
    except Exception as e:
        print(f"  获取安全设置失败: {e}")
    
    # 检查系统属性
    print("\n3. 检查系统属性中的相关项:")
    print("-" * 40)
    
    try:
        result = subprocess.run(
            ["adb", "shell", "getprop"],
            capture_output=True,
            text=True,
            timeout=15
        )
        
        if result.returncode == 0:
            properties = result.stdout.split('\n')
            halo_related = [p for p in properties if any(keyword in p.lower() for keyword in ['halo', 'led', 'light', 'notification']) and p.strip()]
            
            if halo_related:
                for prop in halo_related[:10]:  # 显示前10个相关属性
                    print(f"  {prop}")
            else:
                print("  未找到相关属性")
    except Exception as e:
        print(f"  获取系统属性失败: {e}")


if __name__ == "__main__":
    print("开始测试 Active Halo Lighting 状态检查功能...")
    
    # 测试基本功能
    success = test_halo_lighting_status()
    
    # 调试相关设置
    debug_halo_lighting_settings()
    
    if success:
        print("\n✅ 测试完成!")
        sys.exit(0)
    else:
        print("\n❌ 测试失败!")
        sys.exit(1)

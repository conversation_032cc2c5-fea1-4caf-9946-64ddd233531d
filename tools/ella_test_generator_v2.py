#!/usr/bin/env python3
"""
Ella测试用例生成工具
快速生成标准化的测试脚本
"""
import os
import sys
import json
from pathlib import Path
from typing import Dict, List, Tuple

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from core.logger import log


class EllaTestGenerator:
    """Ella测试用例生成器"""
    
    def __init__(self):
        self.project_root = project_root
        self.config_path = self.project_root / "config" / "status_check_config.json"
        self.output_dir = self.project_root / "testcases" / "test_ella"
        self.supported_apps = self._load_supported_apps()
    
    def _load_supported_apps(self) -> Dict:
        """加载支持的应用配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get("status_check_config", {})
        except Exception as e:
            log.warning(f"加载配置失败: {e}")
            return {}
    
    def detect_command_type(self, command: str) -> Tuple[str, bool]:
        """
        检测命令类型
        
        Returns:
            Tuple[str, bool]: (类型, 是否需要状态验证)
        """
        command_lower = command.lower()
        
        # 应用打开类
        if any(keyword in command_lower for keyword in ["open", "打开", "启动",'play','alarm']):
            return "app_open", True
            
        # 系统设置类
        if any(keyword in command_lower for keyword in ["switch to", "设置", "切换到"]):
            return "system_setting", True
            
        # 第三方集成类
        if any(keyword in command_lower for keyword in [
            "navigate", "order", "download", "search", "call"
        ]):
            return "third_party", False
            
        # 默认为第三方集成类
        return "third_party", False
    
    def detect_app_type(self, command: str) -> str:
        """检测应用类型"""
        command_lower = command.lower()
        
        for app_type, config in self.supported_apps.items():
            keywords = config.get("keywords", [])
            if any(keyword.lower() in command_lower for keyword in keywords):
                return app_type
        
        return "unknown"
    
    def generate_class_name(self, command: str) -> str:
        """生成类名"""
        import re

        def extract_letters_only(text: str) -> str:
            """提取文本中的字母并转换为标题格式"""
            # 只保留字母和空格，去除数字、特殊字符
            letters_only = re.sub(r'[^a-zA-Z\s]', ' ', text)
            # 分割单词并过滤掉常见的无意义词汇
            words = letters_only.split()
            clean_words = [word.title() for word in words if word and word.lower() not in ["a", "the", "to", "and", "or", "of", "in", "on", "at", "by", "for", "with"]]
            return "".join(clean_words)

        command_type, _ = self.detect_command_type(command)

        if command_type == "app_open":
            app_type = self.detect_app_type(command)
            if app_type != "unknown":
                # 对app_type也进行字母提取
                clean_app_type = extract_letters_only(app_type)
                return f"TestEllaOpen{clean_app_type}"
            else:
                # 从命令中提取应用名，只保留字母
                app_command = command.replace("open", "").replace("打开", "").strip()
                app_name = extract_letters_only(app_command)
                return f"TestEllaOpen{app_name}" if app_name else "TestEllaOpenApp"
        else:
            # 从命令生成类名，只保留字母
            clean_command = extract_letters_only(command)
            return f"TestElla{clean_command}" if clean_command else "TestEllaCommand"
    
    def generate_method_name(self, command: str) -> str:
        """生成方法名"""
        import re

        # 只保留字母和空格，去除数字、特殊字符
        letters_only = re.sub(r'[^a-zA-Z\s]', ' ', command)
        # 转换为小写并用下划线连接
        method_name = letters_only.lower().replace(" ", "_")
        # 移除连续的下划线
        while "__" in method_name:
            method_name = method_name.replace("__", "_")
        method_name = method_name.strip("_")

        # 如果处理后为空，使用默认名称
        if not method_name:
            method_name = "command"

        return f"test_{method_name}"
    
    def generate_file_name(self, command: str) -> str:
        """生成文件名"""
        method_name = self.generate_method_name(command)
        return f"{method_name}.py"
    
    def suggest_expected_response(self, command: str) -> List[str]:
        """建议期望响应"""
        command_lower = command.lower()
        
        # 根据命令类型建议响应
        if "open" in command_lower or "打开" in command_lower:
            app_type = self.detect_app_type(command)
            if app_type != "unknown":
                app_config = self.supported_apps.get(app_type, {})
                keywords = app_config.get("keywords", [])
                return ["Done"] + keywords[:1]  # Done + 第一个关键词
            return ["Done"]
        
        if "switch to" in command_lower:
            if "performance" in command_lower or "power saving" in command_lower:
                return ["Sorry"]  # 某些模式可能不支持
            return ["Done"]
        
        if any(word in command_lower for word in ["navigate", "order", "download"]):
            return ["Done"]
        
        return ["Done"]

    def generate_verification_steps(self, assertion_types: List[str], expected_response: List[str],
                                  command_type: str = "third_party", app_name: str = "") -> str:
        """
        根据断言类型生成验证步骤

        Args:
            assertion_types: 断言类型列表，可包含 'text', 'process', 'files'
            expected_response: 期望响应
            command_type: 命令类型 ('app_open', 'third_party', 'system_setting')
            app_name: 应用名称（当command_type为app_open时使用）

        Returns:
            str: 生成的验证步骤代码
        """
        verification_steps = []

        # text断言 - 验证响应包含期望内容
        if 'text' in assertion_types:
            verification_steps.append(f'''
        with allure.step("验证响应包含期望内容"):
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{{expected_text}}，实际响应: '{{response_text}}'"''')

        # process断言 - 验证应用状态
        if 'process' in assertion_types:
            if command_type == "app_open" and app_name:
                verification_steps.append(f'''
        with allure.step(f"验证{app_name}已打开"):
            assert final_status, f"{app_name}: 初始={{initial_status}}, 最终={{final_status}}, 响应='{{response_text}}'"''')
            else:
                verification_steps.append(f'''
        with allure.step(f"验证应用已打开"):
            assert  final_status, f"初始={{initial_status}}, 最终={{final_status}}, 响应='{{response_text}}'"''')

        # files断言 - 验证文件存在
        if 'files' in assertion_types:
            verification_steps.append(f'''
        with allure.step(f"验证文件存在"):
            assert files_status, f"文件不存在！"''')

        return '\n'.join(verification_steps)

    def get_verification_method(self, command: str) -> str:
        """获取验证方法"""
        app_type = self.detect_app_type(command)
        if app_type != "unknown":
            app_config = self.supported_apps.get(app_type, {})
            return app_config.get("final_method", app_config.get("initial_method", ""))
        return ""
    
    def generate_app_open_template(self, command: str, class_name: str, method_name: str,
                                 expected_response: List[str], verification_method: str,
                                 assertion_types: List[str] = None) -> str:
        """生成应用打开类模板"""
        app_name = self.detect_app_type(command)
        if app_name == "unknown":
            app_name = command.replace("open", "").replace("打开", "").strip()

        # 如果没有指定断言类型，使用默认的text和process
        if assertion_types is None:
            assertion_types = ['text', 'process']

        # 生成验证步骤
        verification_steps = self.generate_verification_steps(
            assertion_types, expected_response, "app_open", app_name
        )

        # 根据断言类型确定simple_command_test的参数
        verify_files = 'files' in assertion_types
        verify_status = 'process' in assertion_types

        template = f'''"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class {class_name}(SimpleEllaTest):
    """Ella打开{app_name}测试类"""

    @allure.title("测试{command}")
    @allure.description("测试{command}指令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def {method_name}(self, ella_app):
        """测试{command}命令"""
        command = "{command}"
        expected_text = {expected_response}

        with allure.step(f"执行命令: {{command}}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command, verify_status={verify_status}, verify_files={verify_files}
            )
{verification_steps}

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
'''
        return template
    
    def generate_third_party_template(self, command: str, class_name: str, method_name: str,
                                    expected_response: List[str], assertion_types: List[str] = None) -> str:
        """生成第三方集成类模板"""
        # 如果没有指定断言类型，使用默认的text
        if assertion_types is None:
            assertion_types = ['text']

        # 生成验证步骤
        verification_steps = self.generate_verification_steps(
            assertion_types, expected_response, "third_party"
        )

        # 根据断言类型确定simple_command_test的参数
        verify_files = 'files' in assertion_types
        verify_status = 'process' in assertion_types

        template = f'''"""
Ella语音助手第三方集成指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("第三方集成")
class {class_name}(SimpleEllaTest):
    """Ella {command} 测试类"""
    command = "{command}"
    expected_text = {expected_response}

    @allure.title(f"测试{{command}}能正常执行")
    @allure.description(f"{{command}}")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def {method_name}(self, ella_app):
        f"""{{self.command}}"""

        command = self.command
        expected_text = self.expected_text

        with allure.step(f"执行命令: {{command}}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command, verify_status={verify_status}, verify_files={verify_files}
            )
{verification_steps}

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
'''
        return template

    def generate_unsupported_command_template(self, command: str, class_name: str, method_name: str,
                                            expected_response: List[str], assertion_types: List[str] = None) -> str:
        """生成不支持命令的模板（仅验证响应文本）"""
        # 不支持的命令通常只验证text
        if assertion_types is None:
            assertion_types = ['text']

        # 生成验证步骤
        verification_steps = self.generate_verification_steps(
            assertion_types, expected_response, "unsupported"
        )

        # 根据断言类型确定simple_command_test的参数
        verify_files = 'files' in assertion_types
        verify_status = 'process' in assertion_types

        template = f'''"""
Ella语音助手不支持的指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("不支持的指令")
class {class_name}(SimpleEllaTest):
    """Ella {command} 测试类 - 验证不支持的指令响应"""
    command = "{command}"
    expected_text = {expected_response}

    @allure.title(f"测试{{command}}返回正确的不支持响应")
    @allure.description(f"验证{{command}}指令返回预期的不支持响应")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.smoke
    def {method_name}(self, ella_app):
        f"""{{self.command}} - 验证不支持指令的响应"""

        command = self.command
        expected_text = self.expected_text

        with allure.step(f"执行命令: {{command}}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command, verify_status={verify_status}, verify_files={verify_files}
            )
{verification_steps}

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
'''
        return template
    
    def generate_test_case(self, command: str, expected_response: List[str] = None,
                          output_path: str = None, output_dir_name: str = None,
                          interactive: bool = False, assertion_types: List[str] = None) -> str:
        """
        生成测试用例

        Args:
            command: 要测试的命令
            expected_response: 用户指定的期望响应（可选）
            output_path: 输出路径（可选）
            output_dir_name: 自定义输出目录名称（可选，优先级高于默认目录）
            interactive: 是否启用交互式模式询问期望响应
            assertion_types: 断言类型列表，可包含 'text', 'process', 'files'（可选）

        Returns:
            str: 生成的文件路径
        """
        log.info(f"🚀 开始生成测试用例: {command}")

        # 检测命令类型
        command_type, need_status_verification = self.detect_command_type(command)
        log.info(f"检测到命令类型: {command_type}, 需要状态验证: {need_status_verification}")

        # 生成各种名称
        class_name = self.generate_class_name(command)
        method_name = self.generate_method_name(command)
        file_name = self.generate_file_name(command)

        # 处理期望响应
        if expected_response is None:
            if interactive:
                # 交互式获取用户输入
                expected_response = self._get_expected_response_interactive(command)
            else:
                # 自动建议期望响应
                expected_response = self.suggest_expected_response(command)

        # 获取验证方法
        verification_method = self.get_verification_method(command)

        log.info(f"类名: {class_name}")
        log.info(f"方法名: {method_name}")
        log.info(f"文件名: {file_name}")
        log.info(f"期望响应: {expected_response}")
        log.info(f"验证方法: {verification_method}")

        # 检查是否为不支持的命令（包含sorry或oops）
        response_text = " ".join(expected_response).lower()
        is_unsupported = "sorry" in response_text or "oops" in response_text
        is_dialogue = "dialogue" in output_dir_name

        # 生成代码
        if is_unsupported:
            # 不支持的命令，使用简化模板
            code = self.generate_unsupported_command_template(
                command, class_name, method_name, expected_response, assertion_types
            )
            default_dir_name = "unsupported_commands"
            log.info("检测到不支持的命令，使用简化模板")
        elif command_type == "app_open":
            code = self.generate_app_open_template(
                command, class_name, method_name, expected_response, verification_method, assertion_types
            )
            default_dir_name = "open_app"
        elif is_dialogue:
            code = self.generate_third_party_template(
                command, class_name, method_name, expected_response, assertion_types
            )
            default_dir_name = "open_app"
        else:
            code = self.generate_third_party_template(
                command, class_name, method_name, expected_response, assertion_types
            )
            default_dir_name = "third_coupling"

        # 确定输出路径
        if output_path:
            # 如果提供了完整路径，直接使用
            file_path = Path(output_path)
        else:
            # 优先使用用户指定的目录名称，否则使用默认目录名称
            dir_name = output_dir_name if output_dir_name else default_dir_name
            output_dir = self.output_dir / dir_name
            output_dir.mkdir(parents=True, exist_ok=True)
            file_path = output_dir / file_name

        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(code)

        log.info(f"✅ 测试用例已生成: {file_path}")
        return str(file_path)

    def _get_expected_response_interactive(self, command: str) -> List[str]:
        """
        交互式获取期望响应

        Args:
            command: 命令

        Returns:
            List[str]: 用户输入的期望响应
        """
        print(f"\n📝 为命令 '{command}' 设置期望响应:")

        # 显示建议的响应
        suggested = self.suggest_expected_response(command)
        print(f"💡 建议的期望响应: {suggested}")

        print("\n请选择:")
        print("1. 使用建议的响应")
        print("2. 自定义响应")
        print("3. 输入多个响应项")

        choice = input("请选择 (1/2/3): ").strip()

        if choice == "1":
            return suggested
        elif choice == "2":
            custom_response = input("请输入期望响应: ").strip()
            if custom_response:
                return [custom_response]
            else:
                print("⚠️ 输入为空，使用建议响应")
                return suggested
        elif choice == "3":
            responses = []
            print("请输入期望响应项（每行一个，输入空行结束）:")
            while True:
                response = input(f"响应项 {len(responses) + 1}: ").strip()
                if not response:
                    break
                responses.append(response)

            if responses:
                return responses
            else:
                print("⚠️ 未输入任何响应，使用建议响应")
                return suggested
        else:
            print("⚠️ 无效选择，使用建议响应")
            return suggested


def main():
    """主函数 - 交互式生成器"""
    generator = EllaTestGenerator()

    print("🚀 Ella测试用例生成工具")
    print("=" * 50)
    print("💡 提示: 期望响应将通过交互式方式获取，确保响应内容准确")

    while True:
        print("\n请输入要测试的命令 (输入 'quit' 退出):")
        command = input("> ").strip()

        if command.lower() in ['quit', 'exit', 'q']:
            print("👋 再见!")
            break

        if not command:
            print("❌ 请输入有效的命令")
            continue

        try:
            # 生成测试用例（启用交互式期望响应输入）
            file_path = generator.generate_test_case(command, interactive=True)
            print(f"✅ 测试用例已生成: {file_path}")

            # 询问是否继续
            continue_choice = input("\n是否继续生成其他测试用例? (y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '']:
                break

        except Exception as e:
            print(f"❌ 生成失败: {e}")
            import traceback
            traceback.print_exc()


def generate_with_custom_response(command: str, expected_response: List[str],
                                output_path: str = None, output_dir_name: str = None,
                                assertion_types: List[str] = None) -> str:
    """
    使用自定义期望响应生成测试用例

    Args:
        command: 要测试的命令
        expected_response: 用户指定的期望响应
        output_path: 输出路径（可选，如果提供则忽略output_dir_name）
        output_dir_name: 自定义输出目录名称（可选，优先级高于默认目录）
        assertion_types: 断言类型列表，可包含 'text', 'process', 'files'（可选）

    Returns:
        str: 生成的文件路径
    """
    generator = EllaTestGenerator()

    # 检查expected_response是否包含sorry或oops
    response_text = " ".join(expected_response).lower()
    if "sorry" in response_text or "oops" in response_text:
        # 如果包含sorry或oops，自动设置output_dir_name为unsupported_commands
        output_dir_name = "unsupported_commands"
        log.info(f"检测到不支持的命令响应，自动设置目录为: {output_dir_name}")

    return generator.generate_test_case(command, expected_response, output_path, output_dir_name,
                                      assertion_types=assertion_types)


def batch_generate_with_responses(commands_and_responses: List[Tuple[str, List[str]]],
                                 output_dir_name: str = None, assertion_types: List[str] = None) -> List[str]:
    """
    批量生成测试用例，每个命令都有指定的期望响应

    Args:
        commands_and_responses: 命令和期望响应的元组列表
        output_dir_name: 自定义输出目录名称（可选，所有测试用例将生成到同一目录）
        assertion_types: 断言类型列表，可包含 'text', 'process', 'files'（可选）

    Returns:
        List[str]: 生成的文件路径列表
    """
    generator = EllaTestGenerator()
    generated_files = []

    for command, expected_response in commands_and_responses:
        try:
            file_path = generator.generate_test_case(command, expected_response,
                                                   output_dir_name=output_dir_name,
                                                   assertion_types=assertion_types)
            generated_files.append(file_path)
            print(f"✅ {command} -> {file_path}")
        except Exception as e:
            print(f"❌ 生成失败 '{command}': {e}")

    return generated_files


def batch_generate_with_custom_dirs(commands_responses_dirs: List[Tuple[str, List[str], str]],
                                   assertion_types: List[str] = None) -> List[str]:
    """
    批量生成测试用例，每个命令都有指定的期望响应和目录

    Args:
        commands_responses_dirs: 命令、期望响应和目录名称的元组列表
        assertion_types: 断言类型列表，可包含 'text', 'process', 'files'（可选）

    Returns:
        List[str]: 生成的文件路径列表
    """
    generator = EllaTestGenerator()
    generated_files = []

    for command, expected_response, dir_name in commands_responses_dirs:
        try:
            file_path = generator.generate_test_case(command, expected_response,
                                                   output_dir_name=dir_name,
                                                   assertion_types=assertion_types)
            generated_files.append(file_path)
            print(f"✅ {command} -> {dir_name} -> {file_path}")
        except Exception as e:
            print(f"❌ 生成失败 '{command}': {e}")

    return generated_files


# 使用示例
if __name__ == "__main__":
    # 示例1: 单个测试用例生成，使用自定义目录名称和断言类型
    print("🚀 示例1: 生成单个测试用例到自定义目录")
    command = "close flashlight"
    expected_response = ["Flashlight is turned off now"]
    output_dir_name = "system_coupling"  # 系统耦合
    # output_dir_name = "component_coupling"  # 模块耦合
    # output_dir_name = "third_coupling"  # 三方耦合
    # output_dir_name = "dialogue"  # 聊天
    # output_dir_name = "unsupported_commands"  # 响应未sorry和oops的指令

    # 断言类型配置
    assertion_types = [
        'text',    # 验证响应包含期望内容
        # 'files',   # 验证文件存在
        'process', # 验证应用状态变化
    ]

    file_path = generate_with_custom_response(
        command,
        expected_response,
        output_dir_name=output_dir_name,
        assertion_types=assertion_types
    )
    print(f"✅ 生成完成: {file_path}")

    # # 示例2: 不同断言类型的示例
    # print("\n🚀 示例2: 不同断言类型的示例")
    #
    # # 只验证文本响应
    # print("生成只验证文本响应的测试用例...")
    # file_path_text = generate_with_custom_response(
    #     "say hello",
    #     ["Hello"],
    #     output_dir_name="dialogue",
    #     assertion_types=['text']
    # )
    # print(f"✅ 文本验证测试用例: {file_path_text}")
    #
    # # 验证应用打开状态
    # print("生成验证应用打开状态的测试用例...")
    # file_path_app = generate_with_custom_response(
    #     "open camera",
    #     ["Done"],
    #     output_dir_name="open_app",
    #     assertion_types=['text', 'process']
    # )
    # print(f"✅ 应用状态验证测试用例: {file_path_app}")
    #
    # # 验证文件生成
    # print("生成验证文件生成的测试用例...")
    # file_path_files = generate_with_custom_response(
    #     "take a screenshot",
    #     ["Screenshot saved"],
    #     output_dir_name="component_coupling",
    #     assertion_types=['text', 'files']
    # )
    # print(f"✅ 文件验证测试用例: {file_path_files}")

    # # 示例3: 批量生成到同一自定义目录
    # print("\n🚀 示例3: 批量生成到同一自定义目录")
    # commands_and_responses = [
    #     ("navigate to disneyland", ["Done", "正在为您导航"]),
    #     ("order a burger", ["Sorry", "暂不支持订餐功能"])
    # ]
    # generated_files = batch_generate_with_responses(
    #     commands_and_responses,
    #     output_dir_name="custom_tests",
    #     assertion_types=['text']
    # )
    # print(f"🎉 批量生成完成，共生成 {len(generated_files)} 个文件")
    #
    # # 示例4: 批量生成，每个测试用例使用不同的目录
    # print("\n🚀 示例4: 批量生成，每个测试用例使用不同的目录")
    # commands_responses_dirs = [
    #     ("open bluetooth", ["Done", "蓝牙已开启"], "bluetooth_tests"),
    #     ("open camera", ["Done", "相机已打开"], "camera_tests"),
    #     ("switch to performance mode", ["Done", "性能模式已启用"], "system_settings")
    # ]
    # generated_files = batch_generate_with_custom_dirs(
    #     commands_responses_dirs,
    #     assertion_types=['text', 'process']
    # )
    # print(f"🎉 自定义目录批量生成完成，共生成 {len(generated_files)} 个文件")

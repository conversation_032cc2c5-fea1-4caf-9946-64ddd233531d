#!/usr/bin/env python3
"""
验证Ella应用检测优化效果的简单脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from pages.base.app_detector import AppDetector, AppType
from tools.adb_process_monitor import AdbProcessMonitor


def main():
    """验证Ella检测优化效果"""
    print("🔍 验证Ella应用检测优化效果")
    print("=" * 50)
    
    try:
        # 1. 使用优化后的AppDetector
        app_detector = AppDetector()
        optimized_result = app_detector.check_app_opened(AppType.ELLA)
        
        # 2. 使用传统方法对比
        monitor = AdbProcessMonitor()
        traditional_result = monitor.is_package_running("com.transsion.aivoiceassistant")
        
        print(f"优化后检测结果: {optimized_result}")
        print(f"传统检测结果: {traditional_result}")
        
        # 3. 分析结果
        if not optimized_result and traditional_result:
            print("✅ 优化成功！正确识别Ella应用未启动（仅有后台服务）")
            print("🎯 问题已解决：当前Ella应用未启动，预期返回False，实际返回False")
        elif optimized_result and traditional_result:
            print("⚠️ Ella应用可能真的在前台运行")
        elif not optimized_result and not traditional_result:
            print("ℹ️ Ella应用确实未运行")
        else:
            print("🤔 检测结果异常，需要进一步分析")
            
    except Exception as e:
        print(f"验证失败: {e}")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试优化后的Ella应用检测器
验证是否能正确区分前台应用和后台保活服务
"""

import subprocess
import time
import sys
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from core.logger import log
from pages.base.app_detector import AppDetector, AppType
from tools.adb_process_monitor import AdbProcessMonitor


def test_ella_detection_before_after():
    """对比优化前后的检测结果"""
    print("\n🧪 测试优化后的Ella检测器")
    print("=" * 60)
    
    ella_package = "com.transsion.aivoiceassistant"
    
    try:
        # 1. 使用优化后的AppDetector
        print("1️⃣ 使用优化后的AppDetector:")
        app_detector = AppDetector()
        result_optimized = app_detector.check_app_opened(AppType.ELLA)
        print(f"  优化后检测结果: {result_optimized}")
        
        # 2. 使用AdbProcessMonitor的传统方法作为对比
        print("\n2️⃣ 使用AdbProcessMonitor传统方法（对比）:")
        monitor = AdbProcessMonitor()
        result_traditional = monitor.is_package_running(ella_package, use_fast_method=True)
        print(f"  传统检测结果: {result_traditional}")
        
        # 3. 使用AdbProcessMonitor的活跃检测
        print("\n3️⃣ 使用AdbProcessMonitor活跃检测（对比）:")
        result_active = monitor.is_package_actively_running(ella_package)
        print(f"  活跃检测结果: {result_active}")
        
        # 4. 分析结果
        print("\n📊 结果分析:")
        if result_optimized != result_traditional:
            print("  ✅ 优化成功！检测结果与传统方法不同")
            if not result_optimized and result_traditional:
                print("  🎯 优化后正确识别：应用未启动（仅有后台服务）")
            elif result_optimized and not result_traditional:
                print("  🎯 优化后正确识别：应用已启动（有前台Activity）")
        else:
            print("  ⚠️ 检测结果与传统方法相同，需要进一步分析")
            
        return result_optimized, result_traditional, result_active
        
    except Exception as e:
        print(f"测试失败: {e}")
        return None, None, None


def test_ella_startup_detection():
    """测试Ella应用启动时的检测"""
    print("\n🚀 测试Ella应用启动检测")
    print("=" * 60)
    
    ella_package = "com.transsion.aivoiceassistant"
    
    try:
        app_detector = AppDetector()
        
        # 检测启动前状态
        print("📱 检测启动前状态:")
        before_start = app_detector.check_app_opened(AppType.ELLA)
        print(f"  启动前: {before_start}")
        
        # 尝试启动Ella应用
        print("\n🚀 尝试启动Ella应用...")
        try:
            result = subprocess.run(
                ["adb", "shell", "am", "start", "-n", 
                 f"{ella_package}/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                print("  ✅ 启动命令执行成功")
                time.sleep(3)  # 等待应用启动
                
                # 检测启动后状态
                print("\n📱 检测启动后状态:")
                after_start = app_detector.check_app_opened(AppType.ELLA)
                print(f"  启动后: {after_start}")
                
                if after_start and not before_start:
                    print("  🎯 检测器正确识别了应用启动！")
                elif after_start and before_start:
                    print("  ⚠️ 启动前后都检测为True，可能仍有误判")
                elif not after_start:
                    print("  ❌ 启动后仍检测为False，可能启动失败或检测过严")
                    
                return before_start, after_start
                
            else:
                print(f"  ❌ 启动命令失败: {result.stderr}")
                return before_start, None
                
        except Exception as e:
            print(f"  ❌ 启动应用失败: {e}")
            return before_start, None
            
    except Exception as e:
        print(f"测试失败: {e}")
        return None, None


def analyze_ella_processes():
    """分析Ella进程状态"""
    print("\n🔍 分析Ella进程状态")
    print("=" * 60)
    
    ella_package = "com.transsion.aivoiceassistant"
    
    try:
        monitor = AdbProcessMonitor()
        status_info = monitor.check_package_status(ella_package)
        
        print("📊 当前进程状态:")
        print(f"  是否运行: {status_info.get('is_running', False)}")
        print(f"  是否前台: {status_info.get('is_foreground', False)}")
        print(f"  是否后台: {status_info.get('is_background', False)}")
        print(f"  进程数量: {status_info.get('process_count', 0)}")
        
        if status_info.get('processes'):
            print("\n📋 进程详情:")
            for i, proc in enumerate(status_info['processes'][:5]):  # 只显示前5个
                print(f"  进程{i+1}: {proc.get('name', 'Unknown')}")
                print(f"    PID: {proc.get('pid', 'Unknown')}")
                print(f"    状态: {proc.get('state', 'Unknown')}")
                print(f"    类型: {proc.get('type', 'Unknown')}")
                
        return status_info
        
    except Exception as e:
        print(f"分析失败: {e}")
        return None


def check_current_foreground_app():
    """检查当前前台应用"""
    print("\n📱 检查当前前台应用")
    print("=" * 60)
    
    try:
        # 方法1: 使用dumpsys activity
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "activities", "|", "grep", "mResumedActivity"],
            capture_output=True,
            text=True,
            timeout=5,
            shell=True
        )
        
        if result.returncode == 0 and result.stdout.strip():
            print("📱 当前RESUMED Activity:")
            lines = result.stdout.strip().split('\n')
            for line in lines[:3]:  # 只显示前3行
                print(f"  {line}")
        else:
            print("❌ 无法获取RESUMED Activity")
            
        # 方法2: 使用dumpsys window
        result2 = subprocess.run(
            ["adb", "shell", "dumpsys", "window", "windows", "|", "grep", "mCurrentFocus"],
            capture_output=True,
            text=True,
            timeout=5,
            shell=True
        )
        
        if result2.returncode == 0 and result2.stdout.strip():
            print("\n🎯 当前焦点窗口:")
            focus_line = result2.stdout.strip()
            print(f"  {focus_line}")
            
            # 检查是否是Ella
            if "com.transsion.aivoiceassistant" in focus_line:
                print("  ✅ Ella在前台")
            else:
                print("  ❌ Ella不在前台")
        else:
            print("❌ 无法获取焦点窗口")
            
    except Exception as e:
        print(f"检查前台应用失败: {e}")


def main():
    """主函数"""
    print("🔍 Ella应用检测优化验证")
    print("=" * 80)
    
    # 检查ADB连接
    try:
        result = subprocess.run(["adb", "devices"], capture_output=True, text=True, timeout=5)
        if "device" not in result.stdout:
            print("❌ ADB设备未连接")
            return
        print("✅ ADB设备已连接")
    except Exception as e:
        print(f"❌ ADB连接检查失败: {e}")
        return
    
    # 执行测试
    print("\n" + "=" * 80)
    
    # 1. 测试优化后的检测器
    opt_result, trad_result, active_result = test_ella_detection_before_after()
    
    # 2. 分析当前进程状态
    process_info = analyze_ella_processes()
    
    # 3. 检查当前前台应用
    check_current_foreground_app()
    
    # 4. 测试应用启动检测（可选）
    print("\n" + "=" * 80)
    user_input = input("是否测试Ella应用启动检测？(y/n): ").strip().lower()
    if user_input == 'y':
        before, after = test_ella_startup_detection()
    
    # 总结
    print("\n" + "=" * 80)
    print("🎯 测试总结")
    print("=" * 80)
    
    if opt_result is not None and trad_result is not None:
        print(f"优化后检测结果: {opt_result}")
        print(f"传统检测结果: {trad_result}")
        print(f"活跃检测结果: {active_result}")
        
        if not opt_result and trad_result:
            print("✅ 优化成功！正确识别Ella应用未启动（仅有后台服务）")
        elif opt_result and not trad_result:
            print("✅ 优化成功！正确识别Ella应用已启动")
        elif opt_result == trad_result:
            if process_info and process_info.get('is_background') and not process_info.get('is_foreground'):
                if not opt_result:
                    print("✅ 优化成功！正确识别仅有后台服务")
                else:
                    print("⚠️ 可能需要进一步优化，仍有误判")
            else:
                print("ℹ️ 检测结果一致，符合预期")
    
    print("\n🎯 验证完成")


if __name__ == "__main__":
    main()

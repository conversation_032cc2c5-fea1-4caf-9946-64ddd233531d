#!/usr/bin/env python3
"""
调试主题检测功能
"""

import subprocess
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_theme_settings():
    """调试主题相关的系统设置"""
    print("=" * 60)
    print("调试系统主题检测")
    print("=" * 60)
    
    # 1. 检查ui_night_mode设置
    print("\n1. UI夜间模式设置:")
    print("-" * 30)
    try:
        result = subprocess.run(
            ["adb", "shell", "settings", "get", "secure", "ui_night_mode"],
            capture_output=True,
            text=True,
            timeout=5
        )
        if result.returncode == 0:
            print(f"ui_night_mode: {result.stdout.strip()}")
        else:
            print(f"获取失败: {result.stderr}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 2. 检查dumpsys uimode
    print("\n2. UiMode Manager状态:")
    print("-" * 30)
    try:
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "uimode"],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            output = result.stdout
            lines = output.split('\n')
            for line in lines:
                if any(keyword in line.lower() for keyword in ['night', 'mode', 'configuration', 'uimode']):
                    print(f"  {line.strip()}")
        else:
            print(f"获取失败: {result.stderr}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 3. 检查Activity Configuration
    print("\n3. Activity Configuration:")
    print("-" * 30)
    try:
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "configuration"],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            output = result.stdout
            lines = output.split('\n')
            for line in lines:
                if any(keyword in line.lower() for keyword in ['uimode', 'night', 'theme']):
                    print(f"  {line.strip()}")
        else:
            print(f"获取失败: {result.stderr}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 4. 检查相关系统属性
    print("\n4. 相关系统属性:")
    print("-" * 30)
    properties = [
        "ro.config.ui_night_mode",
        "persist.vendor.theme.mode",
        "ro.vendor.theme.default",
        "persist.sys.theme",
        "ro.config.night_mode"
    ]
    
    for prop in properties:
        try:
            result = subprocess.run(
                ["adb", "shell", "getprop", prop],
                capture_output=True,
                text=True,
                timeout=3
            )
            if result.returncode == 0:
                value = result.stdout.strip()
                if value and value != "null":
                    print(f"  {prop}: {value}")
                else:
                    print(f"  {prop}: (未设置)")
        except:
            print(f"  {prop}: (获取失败)")
    
    # 5. 检查当前应用主题
    print("\n5. 当前应用信息:")
    print("-" * 30)
    try:
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "window", "displays"],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            output = result.stdout
            lines = output.split('\n')
            for line in lines:
                if any(keyword in line.lower() for keyword in ['theme', 'night', 'dark', 'light']):
                    print(f"  {line.strip()}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    debug_theme_settings()

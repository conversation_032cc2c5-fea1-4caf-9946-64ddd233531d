#!/usr/bin/env python3
"""
Active Halo Lighting 状态检查功能使用示例
展示如何在实际项目中使用 Active Halo Lighting 状态检查功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


class HaloLightingAwareTestCase:
    """Halo Lighting 感知的测试用例示例"""
    
    def __init__(self):
        self.checker = SystemStatusChecker()
    
    def setup_test_environment(self):
        """根据 Halo Lighting 状态设置测试环境"""
        try:
            # 检查当前 Halo Lighting 状态
            is_halo_enabled = self.checker.check_active_halo_lighting_status()
            
            if is_halo_enabled:
                log.info("检测到 Active Halo Lighting 已开启，配置相关测试环境")
                self.configure_halo_enabled_test()
            else:
                log.info("检测到 Active Halo Lighting 已关闭，配置标准测试环境")
                self.configure_halo_disabled_test()
                
            return True
            
        except Exception as e:
            log.error(f"设置 Halo Lighting 感知测试环境失败: {e}")
            return False
    
    def configure_halo_enabled_test(self):
        """配置 Halo Lighting 开启时的测试"""
        # 在这里可以设置 Halo Lighting 相关的测试参数
        # 例如：预期的通知行为、LED灯光效果等
        self.expected_notification_behavior = "with_halo_lighting"
        self.led_effects_enabled = True
        self.notification_elements = ["halo_ring", "led_pulse", "notification_light"]
        log.info("Halo Lighting 开启测试环境配置完成")
    
    def configure_halo_disabled_test(self):
        """配置 Halo Lighting 关闭时的测试"""
        # 在这里可以设置标准通知的测试参数
        self.expected_notification_behavior = "standard_notification"
        self.led_effects_enabled = False
        self.notification_elements = ["standard_notification", "sound_only"]
        log.info("标准通知测试环境配置完成")
    
    def validate_notification_behavior(self):
        """验证通知行为一致性"""
        try:
            # 获取详细 Halo Lighting 状态
            halo_status = self.checker.get_active_halo_lighting_detailed_status()
            
            log.info("通知行为一致性验证:")
            log.info(f"  Halo Lighting 状态: {'开启' if halo_status['halo_lighting_enabled'] else '关闭'}")
            log.info(f"  通知LED状态: {'开启' if halo_status['notification_led_enabled'] else '关闭'}")
            log.info(f"  检测方法: {halo_status['detection_method']}")
            
            if halo_status['halo_lighting_enabled']:
                return self.validate_halo_enabled_behavior(halo_status)
            else:
                return self.validate_halo_disabled_behavior(halo_status)
                
        except Exception as e:
            log.error(f"通知行为一致性验证失败: {e}")
            return False
    
    def validate_halo_enabled_behavior(self, halo_status):
        """验证 Halo Lighting 开启时的行为"""
        log.info("验证 Halo Lighting 开启行为")
        
        # 检查是否有相关的系统设置
        if halo_status['system_settings']:
            log.info("✅ 检测到 Halo Lighting 系统设置")
            
            # 验证通知LED脉冲是否开启
            if halo_status['notification_led_enabled']:
                log.info("✅ 通知LED脉冲已开启")
                return True
            else:
                log.warning("⚠️ Halo Lighting 开启但通知LED未启用")
                return False
        else:
            log.warning("⚠️ 未检测到 Halo Lighting 相关设置")
            return False
    
    def validate_halo_disabled_behavior(self, halo_status):
        """验证 Halo Lighting 关闭时的行为"""
        log.info("验证 Halo Lighting 关闭行为")
        
        # 在关闭状态下，应该使用标准通知方式
        if not halo_status['halo_lighting_enabled']:
            log.info("✅ Halo Lighting 已正确关闭")
            return True
        else:
            log.warning("⚠️ Halo Lighting 状态不一致")
            return False


def demonstrate_halo_lighting_detection():
    """演示 Halo Lighting 检测功能"""
    print("=" * 60)
    print("Active Halo Lighting 状态检查功能演示")
    print("=" * 60)
    
    checker = SystemStatusChecker()
    
    # 1. 基本 Halo Lighting 检查
    print("\n1. 基本 Halo Lighting 状态检查:")
    print("-" * 30)
    is_enabled = checker.check_active_halo_lighting_status()
    print(f"Active Halo Lighting: {'开启' if is_enabled else '关闭'}")
    
    # 2. 详细 Halo Lighting 信息
    print("\n2. 详细 Halo Lighting 状态信息:")
    print("-" * 30)
    detailed_status = checker.get_active_halo_lighting_detailed_status()
    
    print(f"Halo Lighting 开启: {detailed_status['halo_lighting_enabled']}")
    print(f"通知LED开启: {detailed_status['notification_led_enabled']}")
    print(f"检测方法: {detailed_status['detection_method']}")
    
    if detailed_status['system_settings']:
        print(f"系统设置数量: {len(detailed_status['system_settings'])}")
    
    if detailed_status['system_properties']:
        print(f"系统属性数量: {len(detailed_status['system_properties'])}")
    
    # 3. Halo Lighting 感知测试示例
    print("\n3. Halo Lighting 感知测试示例:")
    print("-" * 30)
    test_case = HaloLightingAwareTestCase()
    
    if test_case.setup_test_environment():
        print("✅ 测试环境设置成功")
        
        if test_case.validate_notification_behavior():
            print("✅ 通知行为一致性验证通过")
        else:
            print("❌ 通知行为一致性验证失败")
    else:
        print("❌ 测试环境设置失败")


def show_usage_examples():
    """显示使用示例"""
    print("\n" + "=" * 60)
    print("使用示例代码")
    print("=" * 60)
    
    example_code = '''
# 基本使用示例
from pages.base.system_status_checker import SystemStatusChecker

checker = SystemStatusChecker()

# 检查 Active Halo Lighting 是否开启
is_halo_enabled = checker.check_active_halo_lighting_status()
if is_halo_enabled:
    print("Active Halo Lighting 已开启")
else:
    print("Active Halo Lighting 已关闭")

# 获取详细 Halo Lighting 信息
halo_info = checker.get_active_halo_lighting_detailed_status()
print(f"检测方法: {halo_info['detection_method']}")
print(f"通知LED: {halo_info['notification_led_enabled']}")

# 在测试用例中使用
def test_notification_with_halo():
    checker = SystemStatusChecker()
    is_halo_enabled = checker.check_active_halo_lighting_status()
    
    if is_halo_enabled:
        # 验证 Halo Lighting 效果
        assert halo_ring_element.is_visible()
        assert led_pulse_effect.is_active()
    else:
        # 验证标准通知效果
        assert standard_notification.is_visible()
'''
    
    print(example_code)


if __name__ == "__main__":
    # 运行演示
    demonstrate_halo_lighting_detection()
    
    # 显示使用示例
    show_usage_examples()
    
    print("\n" + "=" * 60)
    print("演示完成!")
    print("=" * 60)

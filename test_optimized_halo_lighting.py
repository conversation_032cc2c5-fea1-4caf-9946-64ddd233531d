#!/usr/bin/env python3
"""
测试优化后的 Active Halo Lighting 检测方法
"""

import subprocess
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatus<PERSON>hecker

def test_optimized_detection():
    """测试优化后的检测方法"""
    print("=" * 60)
    print("测试优化后的 Active Halo Lighting 检测方法")
    print("=" * 60)
    
    checker = SystemStatusChecker()
    
    print("\n1. 检测 Active Halo Lighting 状态:")
    print("-" * 40)
    result = checker.check_active_halo_lighting_status()
    print(f"检测结果: {result}")
    print(f"状态: {'开启' if result else '关闭'}")
    
    print("\n2. 获取详细状态信息:")
    print("-" * 40)
    detailed_status = checker.get_active_halo_lighting_detailed_status()
    print(f"Halo Lighting 开启: {detailed_status['halo_lighting_enabled']}")
    print(f"检测方法: {detailed_status['detection_method']}")
    
    if detailed_status['system_settings']:
        print(f"\n系统设置:")
        for setting, value in detailed_status['system_settings'].items():
            print(f"  {setting}: {value}")
    
    if detailed_status['secure_settings']:
        print(f"\n安全设置:")
        for setting, value in detailed_status['secure_settings'].items():
            print(f"  {setting}: {value}")
    
    return result

def check_active_halo_light_setting():
    """直接检查 active_halo_light 设置"""
    print("\n" + "=" * 60)
    print("直接检查 'active_halo_light' 设置")
    print("=" * 60)
    
    settings_to_check = [
        ("system", "active_halo_light"),
        ("secure", "active_halo_light"),
        ("global", "active_halo_light")
    ]
    
    found_settings = []
    
    for namespace, setting in settings_to_check:
        try:
            result = subprocess.run(
                ["adb", "shell", "settings", "get", namespace, setting],
                capture_output=True,
                text=True,
                timeout=3
            )
            
            if result.returncode == 0:
                value = result.stdout.strip()
                print(f"{namespace}.{setting}: {value}")
                if value and value != "null":
                    found_settings.append((namespace, setting, value))
            else:
                print(f"{namespace}.{setting}: (获取失败)")
        except Exception as e:
            print(f"{namespace}.{setting}: (错误: {e})")
    
    if found_settings:
        print(f"\n找到 {len(found_settings)} 个 'active_halo_light' 相关设置:")
        for namespace, setting, value in found_settings:
            status = "开启" if value == "1" else "关闭" if value == "0" else f"未知({value})"
            print(f"  {namespace}.{setting} = {value} ({status})")
    else:
        print("\n未找到任何 'active_halo_light' 设置")
    
    return found_settings

def search_all_halo_settings():
    """搜索所有包含 halo 的设置"""
    print("\n" + "=" * 60)
    print("搜索所有包含 'halo' 的设置")
    print("=" * 60)
    
    all_halo_settings = []
    
    for namespace in ["system", "secure", "global"]:
        try:
            result = subprocess.run(
                ["adb", "shell", "settings", "list", namespace],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                settings = result.stdout.split('\n')
                halo_settings = [s for s in settings if 'halo' in s.lower() and s.strip()]
                
                if halo_settings:
                    print(f"\n{namespace} 命名空间:")
                    for setting in halo_settings:
                        print(f"  {setting}")
                        all_halo_settings.append((namespace, setting))
        except Exception as e:
            print(f"搜索 {namespace} 失败: {e}")
    
    if not all_halo_settings:
        print("\n未找到任何包含 'halo' 的设置")
    
    return all_halo_settings

if __name__ == "__main__":
    print("开始测试优化后的 Active Halo Lighting 检测...")
    
    # 1. 测试优化后的检测方法
    result = test_optimized_detection()
    
    # 2. 直接检查 active_halo_light 设置
    found_settings = check_active_halo_light_setting()
    
    # 3. 搜索所有 halo 相关设置
    all_settings = search_all_halo_settings()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"检测结果: {'开启' if result else '关闭'}")
    print(f"找到 active_halo_light 设置: {len(found_settings)} 个")
    print(f"找到所有 halo 设置: {len(all_settings)} 个")
    
    if found_settings:
        print("\n建议使用的设置:")
        for namespace, setting, value in found_settings:
            print(f"  {namespace}.{setting}")
    
    print("\n✅ 测试完成!")

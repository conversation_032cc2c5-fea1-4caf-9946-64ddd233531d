#!/usr/bin/env python3
"""
测试修复后的 Active Halo Lighting 检测
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatusChecker

def test_fixed_detection():
    """测试修复后的检测"""
    print("=" * 50)
    print("测试修复后的 Active Halo Lighting 检测")
    print("=" * 50)
    
    checker = SystemStatusChecker()
    
    print("\n检测 Active Halo Lighting 状态:")
    print("-" * 30)
    result = checker.check_active_halo_lighting_status()
    print(f"检测结果: {result}")
    print(f"预期结果: True")
    print(f"是否正确: {'✅' if result == True else '❌'}")
    
    print("\n详细状态信息:")
    print("-" * 30)
    detailed = checker.get_active_halo_lighting_detailed_status()
    print(f"检测方法: {detailed['detection_method']}")
    print(f"Halo Lighting 开启: {detailed['halo_lighting_enabled']}")
    
    return result == True

if __name__ == "__main__":
    success = test_fixed_detection()
    if success:
        print("\n✅ 修复成功！")
    else:
        print("\n❌ 仍需进一步调试")

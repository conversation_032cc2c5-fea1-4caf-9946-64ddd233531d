#!/usr/bin/env python3
"""
测试系统音量获取功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


def test_volume_functions():
    """测试音量获取功能"""
    
    print("=" * 60)
    print("测试系统音量获取功能")
    print("=" * 60)
    
    # 创建系统状态检查器实例
    checker = SystemStatusChecker()
    
    # 测试各种音量类型
    volume_types = ["music", "ring", "alarm", "notification", "system", "voice_call"]
    
    print("\n1. 测试各种音量类型获取:")
    print("-" * 40)
    
    for vol_type in volume_types:
        try:
            volume = checker.get_system_volume(vol_type)
            if volume >= 0:
                print(f"✅ {vol_type:12} 音量: {volume}")
            else:
                print(f"❌ {vol_type:12} 音量: 获取失败")
        except Exception as e:
            print(f"❌ {vol_type:12} 音量: 异常 - {e}")
    
    print("\n2. 测试详细音量状态获取:")
    print("-" * 40)
    
    try:
        detailed_status = checker.get_system_volume_detailed_status()
        
        print(f"检测方法: {detailed_status.get('detection_method', 'Unknown')}")
        print(f"音频模式: {detailed_status.get('audio_mode', 'Unknown')}")
        print(f"音频焦点: {detailed_status.get('current_audio_focus', 'Unknown')}")
        
        print("\n各类型音量详情:")
        for vol_type in volume_types:
            volume = detailed_status.get(f'{vol_type}_volume', -1)
            max_vol = detailed_status.get('max_volumes', {}).get(vol_type, 'Unknown')
            percentage = detailed_status.get('volume_percentages', {}).get(vol_type, 'Unknown')
            muted = detailed_status.get('mute_status', {}).get(vol_type, 'Unknown')
            
            status_line = f"  {vol_type:12}: {volume}"
            if max_vol != 'Unknown':
                status_line += f"/{max_vol}"
            if percentage != 'Unknown':
                status_line += f" ({percentage}%)"
            if muted != 'Unknown':
                status_line += f" [{'静音' if muted else '未静音'}]"
            
            print(status_line)
            
    except Exception as e:
        print(f"❌ 获取详细音量状态失败: {e}")
    
    print("\n3. 测试默认音量获取 (媒体音量):")
    print("-" * 40)
    
    try:
        default_volume = checker.get_system_volume()
        if default_volume >= 0:
            print(f"✅ 默认音量 (媒体): {default_volume}")
        else:
            print(f"❌ 默认音量获取失败")
    except Exception as e:
        print(f"❌ 默认音量获取异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


if __name__ == "__main__":
    test_volume_functions()

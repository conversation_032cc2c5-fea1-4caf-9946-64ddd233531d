#!/usr/bin/env python3
"""
测试优化后的详细音量状态方法
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatus<PERSON>he<PERSON>

def test_optimized_detailed_volume():
    """测试优化后的详细音量状态"""
    print("=" * 60)
    print("测试优化后的详细音量状态方法")
    print("=" * 60)
    
    checker = SystemStatusChecker()
    
    print("\n获取详细音量状态:")
    print("-" * 40)
    detailed_status = checker.get_system_volume_detailed_status()
    
    # 显示基本音量信息
    print("基本音量信息:")
    volume_types = ["music", "ring", "alarm", "notification", "system", "voice_call"]
    for vol_type in volume_types:
        volume = detailed_status.get(f'{vol_type}_volume', -1)
        print(f"  {vol_type}音量: {volume}")
    
    # 显示检测方法
    print(f"\n检测方法: {detailed_status.get('detection_method', 'Unknown')}")
    
    # 显示最大音量信息
    max_volumes = detailed_status.get('max_volumes', {})
    if max_volumes:
        print(f"\n最大音量信息:")
        for vol_type, max_vol in max_volumes.items():
            print(f"  {vol_type}最大音量: {max_vol}")
    
    # 显示音量百分比
    percentages = detailed_status.get('volume_percentages', {})
    if percentages:
        print(f"\n音量百分比:")
        for vol_type, percentage in percentages.items():
            print(f"  {vol_type}: {percentage}%")
    
    # 显示静音状态
    mute_status = detailed_status.get('mute_status', {})
    if mute_status:
        print(f"\n静音状态:")
        for vol_type, is_muted in mute_status.items():
            print(f"  {vol_type}: {'静音' if is_muted else '未静音'}")
    
    # 显示音频模式和焦点
    audio_mode = detailed_status.get('audio_mode')
    if audio_mode:
        print(f"\n音频模式: {audio_mode}")
    
    audio_focus = detailed_status.get('current_audio_focus')
    if audio_focus:
        print(f"当前音频焦点: {audio_focus}")
    
    return detailed_status

def compare_with_individual_calls():
    """对比详细状态和单独调用的结果"""
    print("\n" + "=" * 60)
    print("对比详细状态和单独调用的结果")
    print("=" * 60)
    
    checker = SystemStatusChecker()
    
    # 获取详细状态
    detailed_status = checker.get_system_volume_detailed_status()
    
    # 单独调用各个音量类型
    volume_types = ["music", "ring", "alarm", "notification", "system", "voice_call"]
    
    print("\n对比结果:")
    print("-" * 40)
    print(f"{'音量类型':<12} {'详细状态':<8} {'单独调用':<8} {'一致性':<8}")
    print("-" * 40)
    
    all_consistent = True
    for vol_type in volume_types:
        detailed_vol = detailed_status.get(f'{vol_type}_volume', -1)
        individual_vol = checker.get_system_volume(vol_type)
        
        is_consistent = detailed_vol == individual_vol
        if not is_consistent:
            all_consistent = False
        
        consistency = "✅" if is_consistent else "❌"
        print(f"{vol_type:<12} {detailed_vol:<8} {individual_vol:<8} {consistency:<8}")
    
    print("-" * 40)
    if all_consistent:
        print("✅ 所有音量值都一致！优化成功！")
    else:
        print("⚠️ 部分音量值不一致，可能需要进一步优化")
    
    return all_consistent

def test_performance():
    """测试性能改进"""
    print("\n" + "=" * 60)
    print("测试性能改进")
    print("=" * 60)
    
    import time
    
    checker = SystemStatusChecker()
    
    # 测试详细状态方法的执行时间
    print("测试详细状态方法执行时间:")
    start_time = time.time()
    detailed_status = checker.get_system_volume_detailed_status()
    end_time = time.time()
    
    execution_time = end_time - start_time
    print(f"执行时间: {execution_time:.2f} 秒")
    
    # 判断性能
    if execution_time < 5:
        print("✅ 性能良好 (< 5秒)")
    elif execution_time < 10:
        print("⚠️ 性能一般 (5-10秒)")
    else:
        print("❌ 性能较差 (> 10秒)")
    
    # 检查是否使用了优化方法
    detection_method = detailed_status.get('detection_method', '')
    if 'OPTIMIZED' in detection_method:
        print("✅ 使用了优化后的检测方法")
    else:
        print("⚠️ 未使用优化后的检测方法")
    
    return execution_time

if __name__ == "__main__":
    print("开始测试优化后的详细音量状态方法...")
    
    # 1. 测试详细状态
    detailed_status = test_optimized_detailed_volume()
    
    # 2. 对比一致性
    is_consistent = compare_with_individual_calls()
    
    # 3. 测试性能
    exec_time = test_performance()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"检测方法: {detailed_status.get('detection_method', 'Unknown')}")
    print(f"数据一致性: {'✅ 一致' if is_consistent else '❌ 不一致'}")
    print(f"执行时间: {exec_time:.2f} 秒")
    
    # 显示关键音量信息
    music_vol = detailed_status.get('music_volume', -1)
    music_percentage = detailed_status.get('volume_percentages', {}).get('music', 0)
    
    if music_vol > 0:
        print(f"媒体音量: {music_vol} ({music_percentage}%)")
        print("✅ 优化成功！现在能获取实时音量值")
    else:
        print("❌ 媒体音量获取失败")
    
    print("\n🎉 测试完成!")

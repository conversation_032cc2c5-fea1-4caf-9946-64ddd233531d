#!/usr/bin/env python3
"""
测试修复后的音量获取功能
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


def test_volume_methods():
    """测试各种音量获取方法"""
    
    print("=" * 60)
    print("测试修复后的音量获取功能")
    print("=" * 60)
    
    checker = SystemStatusChecker()
    
    # 测试1: 简化版本音量获取 (应该很快)
    print("\n1. 测试简化版本音量获取 (快速):")
    print("-" * 40)
    
    start_time = time.time()
    try:
        simple_volume = checker.get_system_volume_simple()
        end_time = time.time()
        
        print(f"✅ 简化版本完成，耗时: {end_time - start_time:.2f}秒")
        print(f"   媒体音量: {simple_volume.get('music_volume', -1)}")
        print(f"   铃声音量: {simple_volume.get('ring_volume', -1)}")
        print(f"   闹钟音量: {simple_volume.get('alarm_volume', -1)}")
        print(f"   通知音量: {simple_volume.get('notification_volume', -1)}")
        print(f"   检测方法: {simple_volume.get('detection_method', 'Unknown')}")
        print(f"   成功状态: {simple_volume.get('success', False)}")
        
    except Exception as e:
        end_time = time.time()
        print(f"❌ 简化版本失败，耗时: {end_time - start_time:.2f}秒")
        print(f"   错误: {e}")
    
    # 测试2: 单个音量获取
    print("\n2. 测试单个音量获取:")
    print("-" * 40)
    
    volume_types = ["music", "ring", "alarm"]
    for vol_type in volume_types:
        start_time = time.time()
        try:
            volume = checker.get_system_volume(vol_type)
            end_time = time.time()
            print(f"✅ {vol_type:8} 音量: {volume:3d} (耗时: {end_time - start_time:.2f}秒)")
        except Exception as e:
            end_time = time.time()
            print(f"❌ {vol_type:8} 失败: {e} (耗时: {end_time - start_time:.2f}秒)")
    
    # 测试3: 详细状态获取 (可能较慢，但应该不会卡死)
    print("\n3. 测试详细状态获取 (优化后):")
    print("-" * 40)
    
    start_time = time.time()
    try:
        print("开始获取详细状态...")
        detailed_status = checker.get_system_volume_detailed_status()
        end_time = time.time()
        
        print(f"✅ 详细状态获取完成，耗时: {end_time - start_time:.2f}秒")
        print(f"   检测方法: {detailed_status.get('detection_method', 'Unknown')}")
        print(f"   音频模式: {detailed_status.get('audio_mode', 'Unknown')}")
        print(f"   媒体音量: {detailed_status.get('music_volume', -1)}")
        print(f"   铃声音量: {detailed_status.get('ring_volume', -1)}")
        
        # 显示百分比信息（如果有）
        percentages = detailed_status.get('volume_percentages', {})
        if percentages:
            print("   音量百分比:")
            for vol_type, percentage in percentages.items():
                print(f"     {vol_type}: {percentage}%")
        
        # 显示静音状态（如果有）
        mute_status = detailed_status.get('mute_status', {})
        if mute_status:
            print("   静音状态:")
            for vol_type, is_muted in mute_status.items():
                print(f"     {vol_type}: {'静音' if is_muted else '未静音'}")
        
    except Exception as e:
        end_time = time.time()
        print(f"❌ 详细状态获取失败，耗时: {end_time - start_time:.2f}秒")
        print(f"   错误: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


def test_timeout_behavior():
    """测试超时行为"""
    
    print("\n" + "=" * 60)
    print("测试超时行为")
    print("=" * 60)
    
    checker = SystemStatusChecker()
    
    # 设置一个较短的超时时间来测试
    print("\n测试方法在合理时间内完成:")
    print("-" * 40)
    
    methods_to_test = [
        ("简化版本", lambda: checker.get_system_volume_simple()),
        ("单个音量", lambda: checker.get_system_volume("music")),
        ("详细状态", lambda: checker.get_system_volume_detailed_status())
    ]
    
    for method_name, method_func in methods_to_test:
        start_time = time.time()
        try:
            result = method_func()
            end_time = time.time()
            duration = end_time - start_time
            
            if duration < 10:  # 如果在10秒内完成
                print(f"✅ {method_name:8}: 完成 (耗时: {duration:.2f}秒)")
            else:
                print(f"⚠️ {method_name:8}: 完成但较慢 (耗时: {duration:.2f}秒)")
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"❌ {method_name:8}: 失败 (耗时: {duration:.2f}秒) - {e}")


if __name__ == "__main__":
    test_volume_methods()
    test_timeout_behavior()

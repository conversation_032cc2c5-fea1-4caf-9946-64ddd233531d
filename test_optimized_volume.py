#!/usr/bin/env python3
"""
测试优化后的音量检测方法
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatus<PERSON>he<PERSON>

def test_optimized_volume_detection():
    """测试优化后的音量检测"""
    print("=" * 60)
    print("测试优化后的音量检测方法")
    print("=" * 60)
    
    checker = SystemStatusChecker()
    
    print("\n1. 测试媒体音量检测:")
    print("-" * 40)
    volume = checker.get_system_volume("music")
    print(f"媒体音量检测结果: {volume}")
    print(f"说明: 您说当前音量设置到一半，如果最大音量是15，一半应该是7-8左右")
    
    print("\n2. 测试其他音量类型:")
    print("-" * 40)
    volume_types = ["ring", "alarm", "notification", "system", "voice_call"]
    for vol_type in volume_types:
        vol = checker.get_system_volume(vol_type)
        print(f"{vol_type}音量: {vol}")
    
    print("\n3. 获取详细音量状态:")
    print("-" * 40)
    detailed = checker.get_system_volume_detailed_status()
    print(f"检测方法: {detailed.get('detection_method', 'Unknown')}")
    print(f"媒体音量: {detailed.get('media_volume', -1)}")
    print(f"成功状态: {detailed.get('success', False)}")
    
    return volume

def compare_with_settings():
    """对比settings值和实际检测值"""
    print("\n" + "=" * 60)
    print("对比settings值和优化后的检测值")
    print("=" * 60)
    
    import subprocess
    
    # 获取settings中的值
    try:
        result = subprocess.run(
            ["adb", "shell", "settings", "get", "system", "volume_music"],
            capture_output=True,
            text=True,
            timeout=3
        )
        
        if result.returncode == 0:
            settings_volume = result.stdout.strip()
            print(f"settings中的volume_music: {settings_volume}")
        else:
            settings_volume = "获取失败"
            print(f"settings中的volume_music: {settings_volume}")
    except:
        settings_volume = "获取失败"
        print(f"settings中的volume_music: {settings_volume}")
    
    # 获取优化后的检测值
    checker = SystemStatusChecker()
    detected_volume = checker.get_system_volume("music")
    print(f"优化后检测的音量: {detected_volume}")
    
    # 对比结果
    if settings_volume != "获取失败":
        try:
            settings_val = int(settings_volume)
            if detected_volume != settings_val:
                print(f"\n✅ 检测到差异!")
                print(f"   settings值: {settings_val}")
                print(f"   实际检测值: {detected_volume}")
                print(f"   差异: {detected_volume - settings_val}")
            else:
                print(f"\n⚠️ 值相同: {detected_volume}")
                print(f"   可能需要调整音量后再测试")
        except:
            print(f"\n❌ 无法对比，settings值无效")
    
    return detected_volume, settings_volume

if __name__ == "__main__":
    print("开始测试优化后的音量检测...")
    
    # 1. 测试优化后的检测方法
    volume = test_optimized_volume_detection()
    
    # 2. 对比settings值
    detected, settings = compare_with_settings()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"优化后检测结果: {volume}")
    print(f"settings原始值: {settings}")
    
    if volume > 0:
        print("✅ 音量检测成功")
        if volume != 13:
            print("✅ 检测到了与原来不同的音量值，优化有效!")
        else:
            print("⚠️ 仍然是13，可能需要进一步优化或调整音量")
    else:
        print("❌ 音量检测失败")
    
    print("\n建议: 请调整媒体音量到不同位置，然后重新运行测试")
